import numpy as np
from alg.cal_ssmetrics.base_func import mean_greater_than_one


def get_topo_elements_attri_series(result_dict, line_data_list, bus_data_list):
    """
    计算拓扑中的线路和主变相关属性结果时序,(如果是双回线路,合并计算负载率时序, 多主变变电站处理,取负载率最大值);
    :param result_dict:
    :param line_data_list:
    :param bus_data_list:
    :return:
    """
    # 处理线路--多回线路合并;--和平均
    line_power = np.array(result_dict["line_power"])
    line_tunnel_rate = np.zeros((len(line_data_list), line_power.shape[1]))
    i_line = 0
    for row in line_data_list:
        if row['limit'] < 1e-3:
            row['limit'] = 1e-3
        _line_flow = np.sum(line_power[row['related_line_idx']['pos'], :], axis=0) \
                     - np.sum(line_power[row['related_line_idx']['neg'], :], axis=0)
        line_tunnel_rate[i_line] = (np.abs(_line_flow) / row['limit']).round(3)
        i_line += 1
    # limits = np.array([max(row['limit'], 1e-3) for row in line_data_list])
    # # 获取相关的 pos 和 neg 索引
    # pos_indices = [row['related_line_idx']['pos'] for row in line_data_list]
    # neg_indices = [row['related_line_idx']['neg'] for row in line_data_list]
    # # 计算 _line_flow
    # line_flow_pos = np.array([np.sum(line_power[pos_idx, :], axis=0) for pos_idx in pos_indices])
    # line_flow_neg = np.array([np.sum(line_power[neg_idx, :], axis=0) for neg_idx in neg_indices])
    # line_flow = line_flow_pos - line_flow_neg
    # # 计算多回线路的负载率
    # line_tunnel_rate = (np.abs(line_flow) / limits[:, np.newaxis]).round(3)

    # 主变;
    i_line = 0
    trafo_power = result_dict["trafo_power"]
    trafo_tunnel_rate = np.zeros((len(bus_data_list), trafo_power.shape[1]))
    for row in bus_data_list:
        if len(row['related_trafo_idx']) == 0:
            continue
        pf_list = np.divide(trafo_power[row['related_trafo_idx'], :], np.array(row['limit'])[:, np.newaxis])
        trafo_tunnel_rate[i_line] = np.max(np.abs(pf_list), axis=0).round(3)
        i_line += 1

    return line_tunnel_rate, trafo_tunnel_rate


def get_topo_lines_attri_series(result_dict, line_data_list, rlt_key: str = "line_power"):
    """
    计算拓扑中的线路和主变相关属性结果时序,(如果是双回线路,合并计算负载率时序, 多主变变电站处理,取负载率最大值);
    :param result_dict:
    :param line_data_list:
    :return:
    """
    # 处理线路--多回线路合并;--和平均
    if rlt_key not in result_dict.keys():
        return None

    line_power = np.array(result_dict[rlt_key])
    line_tunnel_rate = np.zeros((len(line_data_list), line_power.shape[1]))
    i_line = 0
    for row in line_data_list:
        if row['limit'] < 1e-3:
            row['limit'] = 1e-3
        _line_flow = np.sum(line_power[row['related_line_idx']['pos'], :], axis=0) \
                     - np.sum(line_power[row['related_line_idx']['neg'], :], axis=0)
        line_tunnel_rate[i_line] = (np.abs(_line_flow) / row['limit']).round(3)
        i_line += 1
    return line_tunnel_rate


def cal_topo_elements_loadrate_flags(ele_tunnel_rate, rate_mk=[0.8, 1.0], flag_val=[0, 1, 2]):
    """
    根据设备负载率及门槛值,返回对应标志(是否重载，是否过载标志)
    :param ele_tunnel_rate:
    :param rate_mk:  门槛值
    :param rate_mk:  门槛值对应的flag值;  该 len = len(rate_mk) + 1
    :return:
    """
    # 根据 mk_val 和 flag_val 生成标志矩阵
    flag_matrix = np.digitize(ele_tunnel_rate, bins=rate_mk, right=False)

    # 将 digitize 的结果映射到 flag_val
    flag_matrix = np.array(flag_val)[flag_matrix].astype(int)
    return flag_matrix


def cal_topo_elements_loadrate_hours(ele_tunnel_rate, rate_mk=[0.8, 1.0]):
    """
    计算设备负载率超过门槛值时长;
    line_tunnel_rate: 负载率时长时序;
    :return:
    """
    # 统计line_tunnel_rate每行的>0.8且<1的数目
    heavy_mk, over_mk = rate_mk[0], rate_mk[1]
    ele_heavy_rate_hours = np.sum((ele_tunnel_rate >= heavy_mk) & (ele_tunnel_rate < over_mk), axis=1)
    ele_over_rate_hours = np.sum(ele_tunnel_rate >= over_mk, axis=1)

    # 计算等效越限时长;  >1的时刻的平均负载率 * 实际越限时长
    ele_over_rate_mean = np.apply_along_axis(mean_greater_than_one, 1, ele_tunnel_rate)
    ele_eq_over_hours = ele_over_rate_mean * ele_over_rate_hours

    return ele_heavy_rate_hours, ele_over_rate_hours, ele_eq_over_hours


def cal_element_loadrate_hours(ele_rate, rate_mk=[0.8, 1.0]):
    """
    计算单个设备的设备负载率超过门槛值时长;
    ele_rate: 负载率时长时序;
    :return:
    """
    # 统计>0.8且<1的数目
    heavy_mk, over_mk = rate_mk[0], rate_mk[1]
    ele_heavy_rate_hours = np.sum((ele_rate >= heavy_mk) & (ele_rate < over_mk))
    ele_over_rate_hours = np.sum(ele_rate >= over_mk)

    # 计算等效越限时长;  >1的时刻的平均负载率 * 实际越限时长
    ele_over_rate_mean = mean_greater_than_one(ele_rate, over_mk)
    ele_eq_over_hours = ele_over_rate_mean * ele_over_rate_hours

    return ele_heavy_rate_hours, ele_over_rate_hours, ele_eq_over_hours


def get_newenergy_curtail_info(result_dict: dict):
    """
    获取新能源的相关结果信息: 新能源消纳率/弃电功率/弃电量
    :return:
    """

    curtail_electric_dict = dict()  # 新能源弃电电量:
    curtail_maxpower_dict = dict()  # 新能源最大弃电功率

    num_snaps = result_dict['load'].shape[0]

    def get_maxp_and_curtail_e(rlt_dict, n_type_: str):
        if f'{n_type_}_curtailment' in result_dict.keys():
            curtailment_arr = result_dict[f'{n_type_}_curtailment'].sum(axis=0)
        else:
            curtailment_arr = np.zeros(num_snaps)
        curtail_electric = round(curtailment_arr.sum() * 1e-5, 1)  # 亿千瓦时
        curtail_maxpower = round(np.max(curtailment_arr) * 0.1, 1)  # 万千瓦
        return curtailment_arr, curtail_electric, curtail_maxpower

    wind_curtailment_arr, curtail_electric_dict["wind"], curtail_maxpower_dict["wind"] = get_maxp_and_curtail_e(
        result_dict, "wind")
    solar_curtailment_arr, curtail_electric_dict["solar"], curtail_maxpower_dict["solar"] = get_maxp_and_curtail_e(
        result_dict, "solar")

    total_curtailment_arr = wind_curtailment_arr + solar_curtailment_arr
    curtail_electric_dict["total"] = round(total_curtailment_arr.sum() * 1e-5, 1)
    curtail_maxpower_dict["total"] = round(np.max(total_curtailment_arr) * 0.1, 1)
    return curtail_maxpower_dict, curtail_electric_dict


def get_channel_power_info(net, result_dict: dict, channel_dict: dict, ele_dict: dict):
    """
    传入：通道名称: channel_name; 断面名称:interface_name;获取通道潮流/限额
    :param net:  网络拓扑
    :param result_dict: TEAP计算结果
    :param channel_dict: 通道信息字典;
    :param ele_dict:  设备属性;
    {"channel_name":"","interface_name":""}
    :return:
    """
    rtn_dict = dict()

    channel_name = ele_dict["channel_name"]
    inf_name = ele_dict["interface_name"]
    # channel_dict = CTeapCase.get_inf_collections(net)
    if channel_name not in channel_dict:
        raise ValueError(f"输入的通道名称不在通道列表中：{channel_name}")
    line_dict, trafo_dict = channel_dict[channel_name]["relation"]["line"], \
        channel_dict[channel_name]["relation"]["trafo"]
    line_direction_dict, trafo_direction_dict = channel_dict[channel_name]["relation"]["line_direction"], \
        channel_dict[channel_name]["relation"]["trafo_direction"]
    if inf_name in line_dict.keys():
        inf_line_idx = line_dict[inf_name]
        inf_line_direction = line_direction_dict[inf_name]
        powerflow_arrs = result_dict["line_power"][inf_line_idx, :] * np.array(inf_line_direction)[:,
                                                                      np.newaxis]  # MW
        rtn_dict["powerflow"] = powerflow_arrs.sum(axis=0).round(1).tolist()
        rtn_dict["limit"] = float(net["line"].loc[inf_line_idx, "stable_limit_mw"].values.sum())
    elif inf_name in trafo_dict.keys():
        inf_trafo_idx = trafo_dict[inf_name]
        inf_trafo_direction = trafo_direction_dict[inf_name]
        powerflow_arrs = result_dict["trafo_power"][inf_trafo_idx, :] * np.array(inf_trafo_direction)[:,
                                                                        np.newaxis]  # MW
        rtn_dict["powerflow"] = powerflow_arrs.sum(axis=0).round(1).tolist()
        rtn_dict["limit"] = float(net["trafo"].loc[inf_trafo_idx, "stable_limit_mw"].values.sum())
    else:
        raise ValueError(f"输入的断面名称：{inf_name}不在通道：{channel_name} 关联的断面中")
    return rtn_dict


def calculate_channel_power_ability(channel_inf_dict: dict):
    """
    计算断面.通道的功率输送能力 = 关联设备或断面的功率和/最大负载率
    :param channel_inf_dict:  通道关联断面潮流与限额信息;
    :return:
    """
    # if channel_name not in inf_collection:
    #     raise ValueError(f"输入的通道名称不在通道列表中：{channel_name}")
    #
    # inf_dict = dict()  # 通道字典;
    # for i_c, item in inf_collection[channel_name].items():
    #     ele_dict = {"channel_name": channel_name, "interface_name": i_c}
    #     inf_dict[i_c] = get_channel_power_info(net=net, result_dict=result_dict, channel_dict=inf_collection,
    #                                            ele_dict=ele_dict)
    # 通道潮流sum
    total_sum_pf = np.array([np.array(sub_dict['powerflow']) for sub_dict in channel_inf_dict.values()])
    total_sum_limit = sum([sub_dict['limit'] for sub_dict in channel_inf_dict.values()])
    # 负载率
    total_rate_max = np.array(
        [np.array(sub_dict['powerflow']) / np.array(sub_dict['limit']) for sub_dict in channel_inf_dict.values()])
    total_rate_max[total_rate_max < 1e-3] = 1e-3
    # 通道输电能力
    channel_ability = (total_sum_pf.sum(axis=0) * 0.1 / (total_rate_max).max(axis=0))  # MW->万千瓦;
    channel_ability[channel_ability > total_sum_limit] = total_sum_limit
    channel_ability = channel_ability.round(1).tolist()

    # 每时刻通道关联断面的越限数目(rate>=1.0的行数)
    over_inf_num = np.sum(total_rate_max >= 1.0, axis=0).tolist()
    return channel_ability, over_inf_num
