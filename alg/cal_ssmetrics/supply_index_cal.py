"""
供电裕度分析计算
"""
import pandas as pd
import numpy as np
# import calendar
from alg.cal_ssmetrics.case_info_statistics import CCaseInfo
from alg.cal_ssmetrics.base_func import (GLOBAL_QW_NAME, get_time_range, get_time_step_hours,
                                         spring_month, summer_month, autumn_month, winter_month, daytime_hours)
from alg.ProductionCostSimulation.utils.make_PTDF import make_PTDF_slack
from alg.cal_ssmetrics.zone_func import get_area_level_df_ind


# 一年四季的月份;
# spring_month = [3, 4, 5]
# summer_month = [6, 7, 8]
# autumn_month = [9, 10, 11]
# winter_month = [1, 2, 12]
# daytime_hours = [11, 12, 13, 14]  # 白天光伏应该大发时刻;


def clean_standlize(a, length=None, normalize=False):
    """
    标幺化，并保证值在(0,1)范围内
    roundlize: 是否保留有效位数,保留的小数位数(round_num)
    """
    if a is None:
        b = [0. for l in range(length)]
    else:
        if normalize:  # 标幺化与否
            a = a / a.max()
            a = np.clip(a, 0., 1.)
        a_nan_to_0 = np.nan_to_num(a)
        try:
            b = a_nan_to_0.tolist()
        except:
            b = list(a_nan_to_0)

    return b


def identify_alerts(net, config, res, area_name=None, area_details_dict: dict = dict(), ptdf=None):
    """
    ## 保供六维指标计算分析
    res: teap电力平衡分析结果
    area_name：分区名--如果全局为None
    area_details_dict: 如果area_name的分区不为None;则需要传入的area设备明细:case_info['zone']['device_relay']
    """
    index_rlt_dict = dict()

    # 如果有指定分区，先筛选输入词典中供电分区内信息--及结果数据;
    res_area = get_area_level_df_ind(net, area_name, area_details_dict, res)

    # 新能源总出力、风光分别出力、分别弃电
    # renewable_data, wind_data, solar_data, wind_curtail, solar_curtail = get_renewable_data(res_area, config)

    # 供电裕度分析计算
    power_supply_margin, power_supply_margin_comp, power_supply_margin_threshold, power_supply_margin_fx = cal_power_supply_margin_ind(
        net, res, res_area, config, area_details_dict, area_name, ptdf)

    # 备用容量裕度（只有上限备用）
    reserve_volume, reserve_low_comp, reserve_volume_threshold = cal_reserve_volume(net, config, res_area,
                                                                                    area_details_dict, area_name)

    # 计算爬坡容量(爬坡能力)
    ramp_cap_upward, ramp_cap_upward_comp, ramp_cap_upward_threshold, ramp_cap_downward = cal_ramp_cap_upward(net,
                                                                                                              config,
                                                                                                              res_area,
                                                                                                              area_details_dict,
                                                                                                              area_name)
    # 调峰能力(负备用容量)
    peak_shaving, peak_shaving_comp, peak_shaving_threshold = cal_peak_shaving(net, config, res, res_area,
                                                                               area_details_dict, area_name,ptdf)

    # 区外来电依存度
    feedin_dependence, feedin_dependence_comp, feedin_dependence_threshold = cal_feedin_dependence(config, res_area,
                                                                                                   area_name)

    # (新能源风/光 + feedin)渗透率
    # 无惯量电源渗透率 = 新能源渗透率 + 区外来电依存度
    non_inertia_penetration, non_inertia_penetration_comp, non_inertia_penetration_threshold = cal_non_inertia_penetration(
        config, res_area, area_name)

    # 保留指标结果;
    index_rlt_dict['power_supply_margin'] = power_supply_margin  # 供电裕度
    index_rlt_dict['reserve_low'] = reserve_volume  # 正备用裕度
    index_rlt_dict['peak_shaving'] = peak_shaving  # 调峰能力(负备用容量)
    index_rlt_dict['ramp_cap_upward'] = ramp_cap_upward  # 爬坡容量(爬坡能力)
    index_rlt_dict['feedin_dependence'] = feedin_dependence  # 区外来电依存度
    index_rlt_dict['non_inertia_penetration'] = non_inertia_penetration  # 无惯量电源渗透率

    # 保供六维指标计算公式组成原数据----所有需要的数据,统一放到一起,避免同一数据反复重复存放;
    # 供电裕度计算数据: 主变下送供电容量、可调度电源最大出力能力、新能源出力、负荷有功
    # (上)备用容量裕度：可调度电源开机最大出力能力、可调度电源当前出力、风光的提供备用(0)、负荷有功 * 旋备需求率(load_reserve_need)
    # 爬坡容量(爬坡能力:开机机组爬坡-燃机 3%/min+煤机 1.5%/min): 煤机开机容量、燃机开机容量
    # 调峰能力(负备用容量): 负荷有功、
    # 区外来电依存度:  feedin出力、负荷有功
    # 无惯量电源依存度: wind出力、solar出力、feedin出力、负荷有功

    # 全网/分区推演边界出力详情
    other_metrics_data = dict()
    boundaries = get_boundaries(net, config, area_name, res_area, res, area_details_dict)
    other_metrics_data['boundaries'] = boundaries  # 如果是全网/全省--有直流信息-'feedin_detail_output'

    # 保存分级着色判断边界数据
    thresholds = dict()
    thresholds['reserve_low'] = reserve_volume_threshold
    thresholds['power_supply_margin'] = power_supply_margin_threshold
    thresholds['non_inertia_penetration'] = non_inertia_penetration_threshold
    thresholds['ramp_cap_upward'] = ramp_cap_upward_threshold
    thresholds['peak_shaving'] = peak_shaving_threshold
    thresholds['feedin_dependence'] = feedin_dependence_threshold
    other_metrics_data['thresholds'] = thresholds

    # 整理保存指标计算统一所需的完整数据:
    metric_composition = dict()
    res_area_sum_dict = get_device_rlt_powers(net, config, res_area, area_details_dict, area_name)
    metric_composition['wind'] = res_area_sum_dict['wind_output']  # 风电出力
    metric_composition['solar'] = res_area_sum_dict['solar_output']  # 光伏出力
    metric_composition['load'] = res_area_sum_dict['load']  # 负荷有功
    metric_composition['load_curtailment'] = res_area_sum_dict['load_curtailment']  # 负荷有功
    metric_composition['feedin'] = res_area_sum_dict['feedin_output']  # feedin有功
    metric_composition['gen_on_max'] = res_area_sum_dict['gen_on_max']  # 所有gen机开机容量
    metric_composition['coal_on_max'] = res_area_sum_dict['coal_on_max']  # 煤机开机容量
    metric_composition['gas_on_max'] = res_area_sum_dict['gas_on_max']  # 燃机开机容量
    metric_composition['gen_output'] = res_area_sum_dict['gen_output']  # 煤机出力
    metric_composition['coal_output'] = res_area_sum_dict['coal_output']  # 煤机出力
    metric_composition['gas_output'] = res_area_sum_dict['gas_output']  # 燃机出力
    metric_composition['psm_0'] = power_supply_margin_comp[:, 0]  # 主变下送容量
    metric_composition['dispathable'] = power_supply_margin_comp[:, 1]  # 机组最大可调  gen_max+stogen_output
    metric_composition['feedin_dependence_0'] = feedin_dependence_comp[:, 0]  # 计算外来电依存度时的feedin  gen_max+stogen_output
    metric_composition['peak_shaving_1'] = peak_shaving_comp[:, 1]

    # 另一种存储方式;
    metric_composition['power_supply_margin'] = power_supply_margin_comp
    metric_composition['reserve_low'] = reserve_low_comp
    metric_composition['non_inertia_penetration'] = non_inertia_penetration_comp
    metric_composition['ramp_cap_upward'] = ramp_cap_upward_comp
    metric_composition['peak_shaving'] = peak_shaving_comp
    metric_composition['feedin_dependence'] = feedin_dependence_comp

    # 计算所有原保供指标;
    # if area_name is None:
    #     t_rows_gen = net.gen.index.tolist()
    # else:
    #     t_rows_gen = area_details_dict[area_name]['gen']
    # get_old_metrics_data(net, config, res_area, t_rows_gen, metric_composition)

    return index_rlt_dict, metric_composition, other_metrics_data


def get_boundaries(net, config, area_name, res_area, res, area_details):
    """
    获取分区或全省边界出力
    """
    zero_arr = np.zeros(config['sum_snap'])
    zero_arr.flags.writeable = False  # 矩阵不可修改;

    boundaries = dict()

    centralized_wind_idx = []
    distributed_wind_idx = []
    centralized_solar_idx = []
    distributed_solar_idx = []

    # 分布式光伏容量;
    # 看看feedin中是否有营销分布式 type ='营销分布式'
    if 'feedin_output_solar' in res_area.keys():
        feedin_solar_data = res_area['feedin_output_solar'].sum(axis=0)
    else:
        feedin_solar_data = np.zeros(config['sum_snap'])

    if area_name is None:
        coal_index = net.gen.loc[net.gen.type == 'coal', :].index
        gas_index = net.gen.loc[net.gen.type == 'gas', :].index
        nuclear_index = net.gen.loc[net.gen.type == 'nuclear', :].index
        hydro_index = net.gen.loc[net.gen.type == 'hydro', :].index

        # 增加集中式统调/分布式风电光伏统计
        if config['n_element']['wind'] > 0:
            centralized_wind_idx = net.wind.loc[net.wind.type == 'centralized', :].index
            distributed_wind_idx = net.wind.loc[net.wind.type == 'distributed', :].index
        if config['n_element']['solar'] > 0:
            centralized_solar_idx = net.solar.loc[net.solar.type == 'centralized', :].index
            distributed_solar_idx = net.solar.loc[net.solar.type == 'distributed', :].index
    else:
        bus_in_supply_area = area_details[area_name]['bus']  # 分区关联的bus节点
        regional_gen_index = area_details[area_name]['gen']
        coal_index = net.gen.loc[np.logical_and((net.gen.type == 'coal'),
                                                (np.isin(net.gen.bus, bus_in_supply_area))), :].index
        coal_mask_index = np.isin(coal_index, regional_gen_index)
        coal_index = coal_index[coal_mask_index]  # 这是区域煤电的索引,索引是针对全网的....
        gas_index = net.gen.loc[np.logical_and((net.gen.type == 'gas'),
                                               (np.isin(net.gen.bus, bus_in_supply_area))), :].index
        gas_mask_index = np.isin(gas_index, regional_gen_index)
        gas_index = gas_index[gas_mask_index]
        nuclear_index = net.gen.loc[np.logical_and((net.gen.type == 'nuclear'),
                                                   (np.isin(net.gen.bus, bus_in_supply_area))), :].index
        nuclear_mask_index = np.isin(nuclear_index, regional_gen_index)
        nuclear_index = nuclear_index[nuclear_mask_index]
        hydro_index = net.gen.loc[np.logical_and((net.gen.type == 'hydro'),
                                                 (np.isin(net.gen.bus, bus_in_supply_area))), :].index
        hydro_mask_index = np.isin(hydro_index, regional_gen_index)
        hydro_index = hydro_index[hydro_mask_index]

        # 分区分布式和集中式新能源
        if config['n_element']['wind'] > 0:
            centralized_wind_idx = net.wind.loc[np.logical_and((net.wind.type == 'centralized'),
                                                               (np.isin(net.wind.bus, bus_in_supply_area))), :].index
            distributed_wind_idx = net.wind.loc[np.logical_and((net.wind.type == 'distributed'),
                                                               (np.isin(net.wind.bus, bus_in_supply_area))), :].index
        if config['n_element']['solar'] > 0:
            centralized_solar_idx = net.solar.loc[np.logical_and((net.solar.type == 'centralized'),
                                                                 (np.isin(net.solar.bus, bus_in_supply_area))), :].index
            distributed_solar_idx = net.solar.loc[np.logical_and((net.solar.type == 'distributed'),
                                                                 (np.isin(net.solar.bus, bus_in_supply_area))), :].index

    if 'gen_output' in res.keys():
        boundaries['coal_output'] = res['gen_output'][coal_index, :].sum(axis=0)
        boundaries['gas_output'] = res['gen_output'][gas_index, :].sum(axis=0)
        boundaries['nuclear_output'] = res['gen_output'][nuclear_index, :].sum(axis=0)
        boundaries['hydro_output'] = res['gen_output'][hydro_index, :].sum(axis=0)
    else:
        boundaries['coal_output'] = zero_arr
        boundaries['gas_output'] = zero_arr
        boundaries['nuclear_output'] = zero_arr
        boundaries['hydro_output'] = zero_arr

    boundaries['wind_output'] = res_area['wind_output'].sum(axis=0) if 'wind_output' in res.keys() else zero_arr
    boundaries['solar_output'] = res_area['solar_output'].sum(axis=0) if 'solar_output' in res.keys() else zero_arr
    boundaries['solar_output'] += feedin_solar_data
    boundaries['storage'] = res_area['stogen_output'].sum(axis=0) if 'stogen_output' in res.keys() else zero_arr
    if area_name is None:  # 全省
        boundaries['feedin'] = res['feedin_output'].sum(axis=0)
        boundaries['feedin'] -= feedin_solar_data
        boundaries['feedin_detail_output'] = dict()  # feedin的直流细节;
        for dc_key, dc_val in config['dc_feedin_dict'].items():
            if isinstance(dc_val, dict):
                boundaries['feedin_detail_output'][dc_key] = dict()
                for sub_key, sub_val in dc_val.items():
                    boundaries['feedin_detail_output'][dc_key][sub_key] = res['feedin_output'][sub_val, :].sum(axis=0)
            else:
                boundaries['feedin_detail_output'][dc_key] = res['feedin_output'][dc_val, :].sum(axis=0)
    else:  # 分区的feedin直接使用主变下送功率--分区不显示直流;
        boundaries['feedin'] = np.absolute(res_area['trafo_power'].sum(axis=0))
    boundaries['load'] = np.absolute(res_area['load'].sum(axis=0))

    # 增加集中式统调/分布式风电光伏统计
    if config['n_element']['wind'] > 0:
        boundaries['centralized_wind_output'] = res['wind_output'][centralized_wind_idx, :].sum(axis=0)
        boundaries['distributed_wind_output'] = res['wind_output'][distributed_wind_idx, :].sum(axis=0)
    else:
        boundaries['centralized_wind_output'] = np.zeros(config['sum_snap'])
        boundaries['distributed_wind_output'] = np.zeros(config['sum_snap'])
    if config['n_element']['solar'] > 0:
        boundaries['centralized_solar_output'] = res['solar_output'][centralized_solar_idx, :].sum(axis=0)
        boundaries['distributed_solar_output'] = res['solar_output'][distributed_solar_idx, :].sum(axis=0)
    else:
        boundaries['centralized_solar_output'] = np.zeros(config['sum_snap'])
        boundaries['distributed_solar_output'] = np.zeros(config['sum_snap'])
    boundaries['distributed_solar_output'] += feedin_solar_data

    # 增加区内直流的统计;
    boundaries["dcline"] = dict()
    if "dcline" in net.keys():
        if area_name is None:
            dcline_index = net.dcline.index.tolist()
        else:
            bus_in_supply_area = area_details[area_name]['bus']  # 分区关联的bus节点
            dcline_index = net.dcline[
                (net.dcline.from_bus.isin(bus_in_supply_area)) & (
                    net.dcline.to_bus.isin(bus_in_supply_area))].index.tolist()
        for i_dx in dcline_index:
            idc_name = net.dcline.loc[i_dx, "name"]
            boundaries["dcline"][idc_name] = res['dcline_power_from'][i_dx, :]  # 直流送端功率;
    else:
        pass

    # # 转list-序列化存储--2/3层结构dict;
    for i_key, val in boundaries.items():
        if isinstance(val, dict):
            for key_j, val_j in val.items():
                if isinstance(val_j, dict):
                    for s_j, s_val in val_j.items():
                        boundaries[i_key][key_j][s_j] = (s_val * 0.1).round(1).tolist()
                else:
                    boundaries[i_key][key_j] = (val_j * 0.1).round(1).tolist()
        else:
            boundaries[i_key] = (val * 0.1).round(1).tolist()

    return boundaries


def get_old_metrics_data(net, config, res_area, t_rows_gen, metric_composition):
    """
    计算原风险指标
    """
    ##### 线路和主变负载
    line_power_rate, trafo_power_rate = get_line_trafo_data(net, res_area)

    ##### 新能源总出力、风光分别出力、分别弃电
    renewable_data, wind_data, solar_data, wind_curtail, solar_curtail = get_renewable_data(res_area, config)

    ##### 负荷
    load_data = np.absolute(res_area['load'].sum(axis=0))
    load_data[load_data < 0.0001] = 0.0001

    # 最低区外来电
    feedin_data = None
    feedin_data = res_area['feedin_output'].sum(axis=0)

    # 系统惯量计算 = 所有开机机组的装机容量
    gen_p_max = np.absolute(net.gen.loc[t_rows_gen, 'max_p_mw'].values.reshape(-1, 1))
    gen_on_max = np.multiply(res_area['gen_state'], gen_p_max)
    gen_state = res_area['gen_state']
    gen_p_min = net.gen.loc[t_rows_gen, 'min_p_mw'].values.reshape(-1, 1)
    gen_on_min = np.multiply(gen_state, gen_p_min)

    # 系统惯量
    system_inertia = gen_on_max.sum(axis=0)

    # 需求侧响应
    load_curtailment = res_area['load_curtailment'].sum(axis=0)

    # 最大、最小技术出力
    technical_output_max = gen_on_max.copy().sum(axis=0)
    technical_output_min = gen_on_min.copy().sum(axis=0)

    feedin_dependence = metric_composition['feedin_dependence'][:, -1]
    non_inertia_penetration = metric_composition['non_inertia_penetration'][:, -1]
    peak_shaving_volume = metric_composition['peak_shaving'][:, -1]
    ramp_cap_upward = metric_composition['ramp_cap_upward'][:, -1]
    power_supply_margin = metric_composition['power_supply_margin'][:, -1]
    reserve_volume = metric_composition['reserve_low'][:, -1]

    ## 原风险计算指标
    index_dict = {
        'line_load_rate_max': 3,  # 线路负载最大
        'trafo_load_rate_max': 3,  # 主变负载最大
        'line_variance_max': 2,  # 线路负载方差最大
        'trafo_variance_max': 2,  # 主变负载方差最大
        'renewable_energy_proportion_max': 3,  # 新能源渗透率最高
        'system_inertia_min': 2,  # 系统惯量最小
        'load_max': 1,  # 最大负荷
        'feedin_min': 3,  # 最高区外来电
        'wind_gen_max': 3,  # 风电发电最高
        'solar_gen_max': 3,  # 光伏发电最高
        'wind_curtail_max': 3,  # 风电消纳率最低
        'solar_curtail_max': 3,  # 光伏消纳率最低
        'reserve_low': 3,  # 备用不足
        'ramp_cap_upward': 5,  # 爬坡不足
        'peak_shaving': 5,  # 调峰裕度
        'feedin_dependence': 5,  # 源荷匹配度
        'reserve_volume': 5,  # 备用容量
        'load_curtailment': 5,  # 需求侧响应
        'technical_output_max': 5,  # 最大技术出力
        'technical_output_min': 5,  # 最小技术出力
        'peak_shaving_volume': 5,  # 调峰能力容量
        'power_supply_margin': 5,  # 供电裕度
        'non_inertia_penetration': 5
    }
    alert_time = {a: [] for a in index_dict.keys()}
    old_metrics_data = alert_time.copy()

    for key in index_dict.keys():
        if key == 'line_load_rate_max' and line_power_rate is not None:
            line_power_rate_sum = line_power_rate.sum(axis=0)
            alert_time[key] = list(np.argsort(-line_power_rate_sum, axis=0))[:index_dict[key]]
            old_metrics_data[key] = line_power_rate

        if key == 'trafo_load_rate_max' and trafo_power_rate is not None:
            trafo_power_rate_sum = trafo_power_rate.sum(axis=0)
            alert_time[key] = list(np.argsort(-trafo_power_rate_sum, axis=0))[:index_dict[key]]
            old_metrics_data[key] = trafo_power_rate

        if key == 'line_variance_max' and line_power_rate is not None:
            line_power_rate_variance = np.var(line_power_rate, axis=0)
            alert_time[key] = list(np.argsort(-line_power_rate_variance, axis=0))[:index_dict[key]]
            old_metrics_data[key] = line_power_rate_variance

        if key == 'trafo_variance_max' and trafo_power_rate is not None:
            trafo_power_rate_variance = np.var(trafo_power_rate, axis=0)
            alert_time[key] = list(np.argsort(-trafo_power_rate_variance, axis=0))[:index_dict[key]]
            old_metrics_data[key] = trafo_power_rate_variance

        if key == 'renewable_energy_proportion_max' and renewable_data is not None:
            renewable_energy_proportion = renewable_data / load_data
            alert_time[key] = list(np.argsort(-renewable_energy_proportion, axis=0))[:index_dict[key]]
            old_metrics_data[key] = renewable_energy_proportion

        if key == 'load_max':
            alert_time[key] = list(np.argsort(-load_data, axis=0))[:index_dict[key]]
            old_metrics_data[key] = load_data

        if key == 'feedin_min' and feedin_data is not None:
            alert_time[key] = list(np.argsort(feedin_data, axis=0))[:index_dict[key]]
            old_metrics_data[key] = feedin_data

        if key == 'system_inertia_min':
            alert_time[key] = list(np.argsort(system_inertia, axis=0))[:index_dict[key]]
            old_metrics_data[key] = system_inertia

        if key == 'wind_gen_max' and wind_data is not None:
            alert_time[key] = list(np.argsort(-wind_data, axis=0))[:index_dict[key]]
            old_metrics_data[key] = wind_data

        if key == 'solar_gen_max' and solar_data is not None:
            alert_time[key] = list(np.argsort(-solar_data, axis=0))[:index_dict[key]]
            old_metrics_data[key] = solar_data

        if key == 'wind_curtail_max' and wind_curtail is not None:
            wind_curtail_rate = wind_curtail / (wind_curtail + wind_data)
            alert_time[key] = list(np.argsort(-wind_curtail_rate, axis=0))[:index_dict[key]]
            old_metrics_data[key] = np.nan_to_num(wind_curtail_rate)

        if key == 'solar_curtail_max' and solar_curtail is not None:
            solar_curtail_rate = solar_curtail / (solar_curtail + solar_data)
            alert_time[key] = list(np.argsort(-solar_curtail_rate, axis=0))[:index_dict[key]]
            old_metrics_data[key] = np.nan_to_num(solar_curtail_rate)

        if key == 'reserve_low':
            alert_time[key] = list(np.argsort(reserve_volume, axis=0))[:index_dict[key]]
            old_metrics_data[key] = reserve_volume

        if key == 'peak_shaving':
            alert_time[key] = list(np.argsort(peak_shaving_volume, axis=0))[:index_dict[key]]
            old_metrics_data[key] = peak_shaving_volume

        if key == 'ramp_cap_upward':
            alert_time[key] = list(np.argsort(ramp_cap_upward, axis=0))[:index_dict[key]]
            old_metrics_data[key] = ramp_cap_upward

        if key == 'feedin_dependence':
            alert_time[key] = list(np.argsort(feedin_dependence, axis=0))[:index_dict[key]]
            old_metrics_data[key] = feedin_dependence

        if key == 'power_supply_margin':
            alert_time[key] = list(np.argsort(power_supply_margin, axis=0))[:index_dict[key]]
            old_metrics_data[key] = power_supply_margin

        if key == 'reserve_volume':
            old_metrics_data[key] = reserve_volume

        if key == 'load_curtailment':
            old_metrics_data[key] = load_curtailment

        if key == 'technical_output_max':
            old_metrics_data[key] = technical_output_max

        if key == 'technical_output_min':
            old_metrics_data[key] = technical_output_min

        if key == 'peak_shaving_volume':
            old_metrics_data[key] = peak_shaving_volume

        if key == 'non_inertia_penetration':
            old_metrics_data[key] = non_inertia_penetration

    return alert_time, old_metrics_data


def get_line_trafo_data(net, res_area):
    """
    获取线路和主变负载
    """
    line_power_rate = None
    trafo_power_rate = None

    if 'line_power' in res_area.keys():
        # line_max
        line_max = np.absolute(net.line['stable_limit_mw'].values)
        # line_max = np.absolute(res_area['line_power'].loc[:, 'Pbr_max'].values.reshape(-1, 1))
        line_max[line_max < 0.0001] = 0.0001
        line_power_data = np.absolute(res_area['line_power'])
        line_power_rate = line_power_data / line_max[:, np.newaxis]
    if 'trafo_power' in res_area.keys():
        trafo_max = np.absolute(net.trafo['stable_limit_mw'].values)
        # trafo_max = np.absolute(res_area['trafo_power'].loc[:, 'Pbr_max'].values.reshape(-1, 1))
        trafo_max[trafo_max < 0.0001] = 0.0001
        trafo_power_data = np.absolute(res_area['trafo_power'])
        trafo_power_rate = trafo_power_data / trafo_max[:, np.newaxis]

    return line_power_rate, trafo_power_rate


def get_renewable_data(res_area, config):
    """
    获取新能源总出力、风光分别出力、分别弃电
    """
    n_snap = config['sum_snap']

    wind_data = np.zeros(n_snap)
    solar_data = np.zeros(n_snap)
    if 'wind_output' in res_area.keys():
        wind_data = res_area['wind_output'].sum(axis=0)
    if 'solar_output' in res_area.keys():
        solar_data = res_area['solar_output'].sum(axis=0)

    # 看看feedin中是否有营销分布式 type ='营销分布式'
    if 'feedin_output_solar' in res_area.keys():
        feedin_solar_data = res_area['feedin_output_solar'].sum(axis=0)
        solar_data += feedin_solar_data

    renewable_data = wind_data + solar_data

    wind_curtail = np.zeros(n_snap)
    solar_curtail = np.zeros(n_snap)
    # 风电弃电
    if 'wind_curtailment' in res_area.keys():
        wind_curtail = res_area['wind_curtailment'].sum(axis=0)
    # 光伏弃电
    if 'solar_curtailment' in res_area.keys():
        solar_curtail = res_area['solar_curtailment'].sum(axis=0)
    return renewable_data, wind_data, solar_data, wind_curtail, solar_curtail


def get_device_rlt_powers(net, config, res_area, area_details, area_name):
    """
    获取分区/全网统计数据边界;
    returns:
    dict:
    """
    all_p_output_dict = dict()  # 输出结果dict
    num_snaps = config['sum_snap']
    zeros_1arr = np.zeros(num_snaps)

    #### 风光
    renewable_data, wind_data, solar_data, wind_curtail, solar_curtail = get_renewable_data(res_area, config)
    all_p_output_dict['wind_output'] = wind_data
    all_p_output_dict['solar_output'] = wind_data
    all_p_output_dict['wind_curtailment'] = wind_curtail
    all_p_output_dict['solar_curtailment'] = solar_curtail

    ##### 负荷
    load_data = np.absolute(res_area['load'].sum(axis=0))
    load_data[load_data < 0.0001] = 0
    all_p_output_dict['load'] = load_data
    all_p_output_dict['load_curtailment'] = res_area['load_curtailment'].sum(axis=0)

    # 最低区外来电
    if 'feedin_output' in res_area:
        all_p_output_dict['feedin_output'] = res_area['feedin_output'].sum(axis=0)
    else:
        all_p_output_dict['feedin_output'] = zeros_1arr

    # 系统惯量计算 = 所有开机机组的装机容量
    # 所有开机机组的装机容量
    all_coal_gen_idx = net.gen[net.gen['type'] == 'coal'].index.tolist()
    all_gas_gen_idx = net.gen[net.gen['type'] == 'gas'].index.tolist()
    if area_name is None:
        gen_p_max = np.absolute(net.gen.loc[:, 'max_p_mw'].values.reshape(-1, 1))
        coal_re_idx = all_coal_gen_idx
        gas_re_idx = all_gas_gen_idx
    else:
        t_rows_gen = area_details[area_name]['gen']  # 分区关联的gen的索引;
        coal_re_idx = [i for i in range(len(t_rows_gen)) if t_rows_gen[i] in all_coal_gen_idx]
        gas_re_idx = [i for i in range(len(t_rows_gen)) if t_rows_gen[i] in all_gas_gen_idx]
        # 此处由于是numpy矩阵,需要抽取行序号，不能再是原结果的索引号了

        gen_p_max = np.absolute(net.gen.loc[t_rows_gen, 'max_p_mw'].values.reshape(-1, 1))
    all_gen_output_arr = np.multiply(res_area['gen_state'], gen_p_max)
    all_p_output_dict['gen_on_max'] = all_gen_output_arr.sum(axis=0)  # n*num_snaps ,sum(axis=0) -> 1*num_snaps
    all_p_output_dict['coal_on_max'] = all_gen_output_arr[coal_re_idx, :].sum(axis=0)  # coal--on_max
    all_p_output_dict['gas_on_max'] = all_gen_output_arr[gas_re_idx, :].sum(axis=0)  # gas--on_max

    # 储能充放
    if 'stogen_output' in res_area:
        all_p_output_dict['stogen_output'] = res_area['stogen_output'].sum(axis=0)
    else:
        all_p_output_dict['stogen_output'] = zeros_1arr

    # 水库水电
    if 'hydropower_output' in res_area:
        all_p_output_dict['hydropower_output'] = res_area['hydropower_output'].sum(axis=0)
    else:
        all_p_output_dict['hydropower_output'] = zeros_1arr

    # 机组出力
    all_p_output_dict['gen_output'] = res_area['gen_output'].sum(axis=0)
    all_p_output_dict['coal_output'] = res_area['gen_output'][coal_re_idx, :].sum(axis=0)  # all_coal-p
    all_p_output_dict['gas_output'] = res_area['gen_output'][gas_re_idx, :].sum(axis=0)  # all_gas-p

    return all_p_output_dict


def get_above_vn_kv_trafoidx(net, vn_kv: float = 500):
    area_trafo_selected_idex = net.trafo[(net.trafo['vn_hv_kv'] >= (vn_kv * 0.95))].index
    if len(area_trafo_selected_idex) == 0:
        # 取net.trafo中'vn_hv_kv'最高的值的行索引
        area_trafo_selected_idex = net.trafo[net.trafo['vn_hv_kv'] >= (net.trafo['vn_hv_kv'].max() * 0.95)].index
    return area_trafo_selected_idex

def get_zone_trafo_cap(net, area_name, area_details, trafo_all_power, load_ori, ptdf):
    """
    使用负荷增量法计算分区的主变下送容量
    net: topo网络
    config：配置选项
    area_name： 分区名称
    area_details： 分区关联设备索引
    res: teap原始计算结果
    res_area ： teap计算结果经过分区筛选后的分区结果
    ptdf:潮流分布转移因子
    """
    if area_name is None or area_name == '' or area_name == '全省':
        raise Exception('分区名称错误')

    num_snaps = len(load_ori[0,:])
    num_lines = net.line.shape[0]
    # 负荷增量计算法--只考虑(500kV主变)  # 筛选分区== area_name && vn_hv_kv>=500*0.95--如果没有500kV主变的就不能计算分区--走不到这里
    area_trafo_selected_idex = get_above_vn_kv_trafoidx(net)
    area_trafo_idex_ori = np.array(area_details[area_name]['trafo'])
    # 提取area_trafo_selected_idex中在area_trafo_idex的元素
    area_trafo_idex = area_trafo_idex_ori[np.in1d(area_trafo_idex_ori, area_trafo_selected_idex)]
    blow_side = False  # 功率取自低压侧;
    if len(area_trafo_idex) == 0:
        # 找分区内的变压器的最高等级的索引
        vn_kv_zone = net.trafo.loc[area_details[area_name]['trafo'], 'vn_hv_kv'].max()
        area_trafo_idex_new = get_above_vn_kv_trafoidx(net, vn_kv=vn_kv_zone)
        area_trafo_idex = area_trafo_idex_ori[np.in1d(area_trafo_idex_ori, area_trafo_idex_new)]
        blow_side = True
    if len(area_trafo_idex) > 0:
        trafo_power = trafo_all_power[area_trafo_idex, :]
        if blow_side:
            trafo_power = trafo_power * (-1)
        area_load_idx = np.array(area_details[area_name]['load'])
        if len(area_load_idx) > 0:
            # 两种情况, 有interface 就使用断面限额计算下送容量；否则使用trafo的limit
            # load_c = res_area['load']
            # load_c += res_area['load_curtailment']
            load_c = load_ori[area_load_idx, :]
            area_load_bus = net.load.loc[area_load_idx, 'bus'].values
            ptdf_c = ptdf[area_trafo_idex + num_lines, :][:, area_load_bus]  # load 及ptdf情况
            # 开始分情况
            trafo_interface_inds = net.trafo.loc[area_trafo_idex, "interface"].unique()  # 这里应该保留从小到大的排序
            trafo_interface_inds = [x for x in trafo_interface_inds if not pd.isna(x)]  # 去除nan值
            if len(trafo_interface_inds) > 0:
                buse_inteface_limit = True
                # 建立单台主变与主变断面之间的关联矩阵
                rela_mat = np.zeros((len(area_trafo_idex), len(trafo_interface_inds)))
                for i, trafo_ind in enumerate(area_trafo_idex):
                    for j, trafo_interface_ind in enumerate(trafo_interface_inds):
                        trafo_ind_inf_no = net.trafo.loc[trafo_ind, "interface"]
                        if not np.isnan(trafo_ind_inf_no) and int(trafo_interface_ind) == int(trafo_ind_inf_no):
                            rela_mat[i, j] = 1
                limit = net.interface.loc[trafo_interface_inds, 'max_p_mw'].values

                ptdf_c[np.abs(ptdf_c) < 0.02] = 0
                # 计算ptdf_c整体的最大值
                if np.nanmax(np.abs(ptdf_c)) < 0.001:  # 校验是否有真实的灵敏度关系--主变和分区负荷无关联--或者负荷全为0
                    trafo_supply_cap = np.zeros(num_snaps)
                elif np.nanmax(np.abs(load_c)) < 0.001:
                    trafo_supply_cap = limit.sum(axis=0)  # 负荷全为0，无法用负荷增量法；
                else:
                    if buse_inteface_limit:
                        load_flow_interface = (-1 * (rela_mat.T @ (ptdf_c @ load_c)))
                        power_interface = rela_mat.T @ trafo_power  # 主变断面的降压功率
                    else:
                        load_flow_interface = (-1 * (ptdf_c @ load_c))
                        power_interface = trafo_power
                    k = 1 + (limit[:, np.newaxis] - power_interface) / load_flow_interface
                    k = k[np.isfinite(k).any(axis=1)]
                    k_min = np.nanmin(k, axis=0)
                    trafo_supply_cap = (power_interface + load_flow_interface * (k_min - 1)).sum(axis=0)
            else:
                # 直接采用主变容量- 主变功率计算;
                limit = net.trafo.loc[area_trafo_idex, 'stable_limit_mw'].values
                trafo_supply_cap = np.full((num_snaps,), limit.sum(axis=0))
        else:
            # 没有负荷--主变下送容量就是主变的容量和;
            trafo_supply_cap = net.trafo.loc[area_trafo_idex, 'stable_limit_mw'].values.sum(axis=0)
            # 将trafo_supply_cap扩展成num_snaps列
            trafo_supply_cap = np.full((num_snaps,), trafo_supply_cap)
    else:
        # 没有主变--主变下送容量就是0;
        trafo_supply_cap = np.zeros(num_snaps)
    trafo_supply_cap[trafo_supply_cap < 0] = 0  # 不能<0

    return trafo_supply_cap


def cal_power_supply_margin_ind(net, res, res_area, config, area_details, area_name, ptdf=None):
    """
    计算分区/全网的供电裕度
    net: 全网网络拓扑;
    res_area: 分区的结果数据
    config: 配置文件
    area_details： 分区关联设备索引
    ptdf: 功率传输分布因子;
    """
    num_snaps = config['sum_snap']

    # 风光
    renewable_data, wind_data, solar_data, wind_curtail, solar_curtail = get_renewable_data(res_area, config)

    # 需求侧响应
    load_curtailment = res_area['load_curtailment'].sum(axis=0)

    # 负荷
    load_data = np.absolute(res_area['load'].sum(axis=0))
    load_data[load_data < 0.0001] = 0.0001

    load_data += load_curtailment  # ctl-20241108

    # 负荷备用系数数据
    # load_up_reserve_cof = config['up_reserve_cof']
    # load_data = load_data * (1 + load_up_reserve_cof)  # 考虑备用--因为缺电是考虑备用硬约束进行削减的

    # 系统惯量计算
    if area_name is None:
        gen_idx = net.gen.index.tolist()
    else:
        gen_idx = list(area_details[area_name]['gen'])
    gen_ori_p_max = np.absolute(net.gen.loc[:, 'max_p_mw'].values.reshape(-1, 1))
    # gen_on_max_p_mw = np.multiply(res['gen_state'], gen_ori_p_max)[gen_idx, :]  # 240713 18:00又改回来了
    # # 20240713-丁/廖:采用时序的最大出力曲线;
    gen_p_maxrate_series = CCaseInfo.get_gen_pmax_series(net, config, 'gen_p_max')
    gen_on_max_p_mw = (res['gen_state'] * gen_p_maxrate_series * gen_ori_p_max)[gen_idx, :]

    # 储能充放
    stogen_data = res_area['stogen_output'].sum(axis=0) if 'stogen_output' in res_area.keys() else np.zeros(num_snaps)

    # 水库水电
    # hydropower_data = np.zeros(num_snaps)
    # if 'hydropower_output' in res_area.keys():
    #     hydropower_data = res_area['hydropower_output'].sum(axis=0)

    # 供电裕度
    dispatchable = gen_on_max_p_mw.sum(axis=0) + stogen_data

    zone_feedin_idx = [] # 分区内关联的feedin
    if 'feedin_output' in res.keys():
        if area_name is None or area_name == GLOBAL_QW_NAME:
            feedin_idx = net.feedin[net.feedin['type'] != "营销分布式"].index
            feedin_data = res['feedin_output'][feedin_idx, :].sum(axis=0)
        else:
            feedin_df = net.feedin[net.feedin.index.isin(area_details[area_name]['feedin'])]
            if len(feedin_df) > 0:
                feedin_df["vn_kv"] = net.bus.loc[feedin_df["bus"],"vn_kv"].values
                zone_feedin_idx = feedin_df[feedin_df['vn_kv'] < 500*0.9].index.tolist()  # 落点在分区内,且电压等级小于500kV
    else:
        feedin_data = np.zeros(num_snaps)
    # feedin中的区内直流(vn_kv<500*0.9)受入为正
    if len(zone_feedin_idx) > 0:
        dispatchable += res['feedin_output'][zone_feedin_idx, :].sum(axis=0)

    # 区内直流;江苏:泰扬北和访晋分区的游圌直流;  读取数据的时候 符号已经*(-1)
    for i_derec in ["from","to"]:
        if f"dcline_power_{i_derec}" in res.keys() and not (area_name is None or area_name == GLOBAL_QW_NAME):
            # 直流首端在分区内的线路
            zone_dcline_idx = net.dcline[net.dcline[f'{i_derec}_bus'].isin(area_details[area_name]['bus'])].index.tolist()
            if len(zone_dcline_idx) > 0:
                dispatchable -= res[f'dcline_power_{i_derec}'][zone_dcline_idx, :].sum(axis=0)

    # 新能源的供电支撑能力
    if 'new_confidence' in config.keys() and config['new_confidence']:
        ## 风光出力以各自装机的10%为天花板 ##
        t_rows_wind = area_details[area_name]['wind']
        wind_cap = net.wind.loc[t_rows_wind, 'max_p_mw'].values.sum() if len(net.wind) > 0 else 0.0
        t_rows_solar = area_details[area_name]['solar']
        solar_cap = net.solar.loc[t_rows_solar, 'max_p_mw'].values.sum() if len(net.solar) > 0 else 0.0
        renewable = np.clip(renewable_data, None, 0.1 * (wind_cap + solar_cap))
    else:
        renewable = renewable_data

    if area_name is not None:
        # 使用负荷增量法计算主变下送容量
        # 生成ptdf
        if ptdf is None:
            net_ptdf = make_PTDF_slack(net, select_idx=config['selected_branch_idx'], config=config,
                                       distributed_slack=True)
            # num_lines = net.line.shape[0]  # 此处是因为在构建ptdf的时候,支路：先线路+主变支路,进行了叠加,找主变索引,需要加上前面的线路的数目索引偏移
        else:
            net_ptdf = ptdf
        trafo_power = res["trafo_power"]
        load_power = res["load"] + res["load_curtailment"]
        trafo_supply_cap = get_zone_trafo_cap(net, area_name, area_details, trafo_power, load_power, net_ptdf)

        # 2024年7月10日 佳煜决定用实际负荷计算
        # 240713 18:00又改回来了--用原始负荷计算
        # power_supply_margin = trafo_supply_cap + dispatchable + renewable - load_data
        power_supply_margin = trafo_supply_cap + dispatchable + renewable - load_data
        # power_supply_margin_mean = np.mean(power_supply_margin)
        # power_supply_margin_min = np.min(power_supply_margin)
        # power_supply_margin_min_idx = np.argmin(power_supply_margin)
        power_supply_margin_fx = trafo_supply_cap + dispatchable + renewable - load_data
        power_supply_margin_comp = np.concatenate((trafo_supply_cap,
                                                   dispatchable,
                                                   renewable,
                                                   load_data,
                                                   power_supply_margin)
                                                  ).reshape((-1, 5), order='F')
        # if area_name == '盐城中':
        #     _tm_df = get_time_range(config['start_time'], config['sum_snap'])
        #     zn_margin_info_df = pd.DataFrame({
        #         '时刻': _tm_df.index.tolist(),
        #         '主变供电能力': trafo_supply_cap,
        #         '常规容量加储能加区内直流': dispatchable,
        #         '风光': renewable,
        #         '实际负荷': load_data,
        #         '供电裕度': power_supply_margin,
        #     })
        #     zn_margin_info_df.to_csv(f'./{area_name}_zn_margin_info_df.csv', encoding='gbk', index=True)
    else:
        # 全网考虑:区外来电

        # 分析常规机组是否有受阻时序曲线"gen_p_max":
        # gen_pmax_rate_series = CCaseInfo.get_gen_pmax_series(net, config, 'gen_p_max')
        # gen_dbz = (gen_on_max_p_mw * (1 - gen_pmax_rate_series)).sum(axis=0)

        # 大煤电机组的上备用能力: timeseries,type == 'gen_capacity_max'
        gen_reserve_maxrate_series = CCaseInfo.get_gen_pmax_series(net, config, 'gen_capacity_max')
        gen_dbz = (gen_on_max_p_mw * (1 - gen_reserve_maxrate_series)).sum(axis=0)  # 无法提供备用的容量

        # 上备用e需求时序曲线: timesries,type == 'up_reserve'
        # 找出net.timeseries中'type' == 'up_reserve'的时序曲线,如果没有就是np.zeros(num_snaps)
        up_reserve_need_series_df = net.timeseries[net.timeseries['type'] == 'up_reserve']
        if len(up_reserve_need_series_df) > 0:
            # 取up_reserve_need_series_df的第一行的第2：列
            up_reserve_need_series = up_reserve_need_series_df.iloc[0, 2:].values.astype(float)
        else:
            # 使用config中旋备备用率
            up_reserve_need_series = (load_data * config["up_reserve_cof"]).astype(float)
        # 2024年7月10日 佳煜决定用实际负荷计算
        # 2025.01.17 佳煜计算全网供电裕度时提出-up_reserve_need_series
        # 25.01.20 ctl改为:去除up_reserve_need_series和gen_dbz,计算结果和load_curtailment一致;
        power_supply_margin = feedin_data + dispatchable + renewable - load_data  # - gen_dbz # up_reserve_need_series
        power_supply_margin_fx = feedin_data + dispatchable + renewable - load_data  # - gen_dbz # up_reserve_need_series
        power_supply_margin_comp = np.concatenate((feedin_data,
                                                   dispatchable,
                                                   renewable,
                                                   load_data,
                                                   gen_dbz,
                                                   np.tile([0], (num_snaps,)),  # np.tile([3200],(num_snaps,)),
                                                   power_supply_margin)
                                                  ).reshape((-1, 7), order='F')
    # print('供电裕度(万千瓦)', area_name, power_supply_margin.mean()/10)
    # 分区紧张的判断依据计算：分区最大负荷的5%或10万千瓦，取二者最大值）
    power_supply_margin_threshold = np.tile([-200, 0, max(100, 0.05 * load_data.max()), 400],
                                            (num_snaps, 1)) if area_name is not None else np.tile(
        [-3000, 0, 3000, 6000], (num_snaps, 1))

    return power_supply_margin, power_supply_margin_comp, power_supply_margin_threshold, power_supply_margin_fx


def cal_reserve_volume(net, config, res_area, area_details, area_name):
    """
    计算备用容量裕度(上备用,备用缺口)
    args:
    res_area: 分区的设备结果(array)
    area_details:dict,分区设关系;
    area_name: str,分区名称;
    returns:
    reserve_volume: 上备用
    reserve_low_comp: 计算上备用的数据构成; np.array

    """
    num_snaps = config['sum_snap']
    zeros_1arr = np.zeros(num_snaps)

    #### 风光
    renewable_data, wind_data, solar_data, wind_curtail, solar_curtail = get_renewable_data(res_area, config)

    ##### 负荷
    load_data = np.absolute(res_area['load'].sum(axis=0))
    load_data[load_data < 0.0001] = 0.0001
    load_reserve_need = 0.05 * load_data

    # 所有开机机组的装机容量
    if area_name is None:
        gen_p_max = np.absolute(net.gen.loc[:, 'max_p_mw'].values.reshape(-1, 1))
    else:
        t_rows_gen = area_details[area_name]['gen']  # 分区关联的gen的索引;
        gen_p_max = np.absolute(net.gen.loc[t_rows_gen, 'max_p_mw'].values.reshape(-1, 1))
    gen_on_max = np.multiply(res_area['gen_state'], gen_p_max)

    # 机组出力
    gen_output = res_area['gen_output']

    # 备用容量裕度计算
    reserve_up_volume = gen_on_max.sum(axis=0) - gen_output.sum(axis=0) - load_reserve_need

    if config['n_element']['wind'] > 0:
        wind_reserve = config['wind_reserve_cof'] * (wind_data + wind_curtail)  # 风光提供备用？--可以置为0;
        reserve_up_volume -= wind_reserve
    else:
        wind_reserve = zeros_1arr

    if config['n_element']['solar'] > 0:
        solar_reserve = config['solar_reserve_cof'] * (solar_data + solar_curtail)
        reserve_up_volume -= solar_reserve
    else:
        solar_reserve = zeros_1arr

    # if area_name is None:
    #     # 全省层面备用直接用供电裕度，但是若大于320万千瓦，按320记，最小为0。
    #     reserve_volume = np.clip(power_supply_margin, 0, 3200)

    reserve_up_volume = np.clip(reserve_up_volume, 0, None)

    reserve_low_comp = np.concatenate((gen_on_max.sum(axis=0) - gen_output.sum(axis=0),
                                       wind_reserve + solar_reserve,
                                       load_reserve_need,
                                       reserve_up_volume)).reshape((-1, 4), order='F')

    reserve_volume_threshold = np.tile([-3000, 0, 3000, 6000],
                                       (num_snaps, 1))  # 行复制num_snaps次;
    return reserve_up_volume, reserve_low_comp, reserve_volume_threshold


def cal_ramp_cap_upward(net, config, res_area, area_details, area_name):
    """
    计算爬坡能力：
    ramp_cap_upward：上爬坡能力
    ramp_cap_upward_comp： 上爬坡计算数据
    ramp_cap_upward_threshold： 上爬坡紧张度
    ramp_cap_downward:下爬坡能力
    """
    num_snaps = config['sum_snap']

    res_rlt_dict = get_device_rlt_powers(net, config, res_area, area_details, area_name)

    load_data = res_rlt_dict['load']  # 负荷
    feedin_data = res_rlt_dict['feedin_output']  # 区外来电
    gen_on_max = res_rlt_dict['gen_on_max']  # 所有开机机组的装机容量和
    stogen_data = res_rlt_dict['stogen_output']  # 储能充放
    hydropower_data = res_rlt_dict['hydropower_output']  # 水库水电
    gen_output = res_rlt_dict['gen_output']  # 机组出力
    wind_data = res_rlt_dict['wind_output']  # 风电出力
    solar_data = res_rlt_dict['solar_output']  # 光伏出力

    # 净负荷 = 总负荷 - 风 - 光 - 储能抽蓄 - 水电 -区外
    # net_load = load_data.copy() - wind_data - solar_data - stogen_data - hydropower_data - feedin_data
    # net_load_diff = np.diff(net_load)
    # net_load_diff = np.append(net_load_diff, 0.)  # the last element is dummy

    # 连续运行机组可用爬坡容量
    if area_name is None:
        t_rows_gen = net.gen.index
    else:
        t_rows_gen = area_details[area_name]['gen']  # 分区关联的gen的索引;

    ramp_cap_all_gen = net.gen.loc[t_rows_gen, 'ramp_per_hour_mw'].values.reshape(-1, 1)
    gen_state = res_area['gen_state']
    oper_hour = res_area['oper_hour']
    gen_p_min = net.gen.loc[t_rows_gen, 'min_p_mw'].values.reshape(-1, 1)
    gen_p_max = net.gen.loc[t_rows_gen, 'max_p_mw'].values.reshape(-1, 1)
    gen_min_off_hours = net.gen.loc[t_rows_gen, 'min_off_hour'].values.reshape(-1, 1)
    gen_min_on_hours = net.gen.loc[t_rows_gen, 'min_on_hour'].values.reshape(-1, 1)
    ramp_cap_on_gen = np.multiply(gen_state, ramp_cap_all_gen).sum(axis=0)
    ramp_cap_upward = np.minimum((gen_on_max - gen_output), ramp_cap_on_gen)
    # 可开机组可用爬坡容量
    off_gen = (gen_state == 0)
    greater_than_min_off_hours = (oper_hour >= gen_min_off_hours)
    may_be_turned_on_gens = np.multiply(off_gen, greater_than_min_off_hours)
    may_be_turned_on_gens_cap = np.multiply(may_be_turned_on_gens, gen_p_min).sum(axis=0)
    # 总可用爬坡容量
    ramp_cap_upward += may_be_turned_on_gens_cap
    ramp_cap_upward_comp = np.concatenate((ramp_cap_upward,
                                           may_be_turned_on_gens_cap,
                                           ramp_cap_upward
                                           )).reshape((-1, 3),
                                                      order='F')

    # load_diff = np.diff(load_data)
    # load_diff = np.append(load_diff, 0.)  # the last element is dummy

    ######## 电科院新定义 ########

    ## 电科院对爬坡能力新定义：燃机 3%每分钟+煤机 1.5%每分钟
    # 分类型开机容量
    gen_p_max = np.absolute(net.gen.loc[t_rows_gen, 'max_p_mw'].values.reshape(-1, 1))
    gen_on_max = np.multiply(res_area['gen_state'], gen_p_max)

    installed_cap = net.gen.loc[t_rows_gen, ['type', 'max_p_mw']].groupby(['type']).sum()['max_p_mw']
    gen_on_max_of_types = pd.DataFrame()
    for type_ in installed_cap.index:
        gens_in_region = t_rows_gen.copy()
        gens_of_type = net.gen.loc[gens_in_region, 'type'] == type_
        gen_on_max_of_types[type_] = (gen_on_max * gens_of_type.values[:, np.newaxis]).sum(axis=0)
    try:
        gas_ramp = 0.03 * gen_on_max_of_types['gas']
    except:
        gas_ramp = np.array(num_snaps * [0])
    try:
        coal_ramp = 0.015 * gen_on_max_of_types['coal']
    except:
        coal_ramp = np.array(num_snaps * [0])

    ramp_cap_upward = gas_ramp + coal_ramp
    ramp_cap_upward_comp = np.concatenate((gas_ramp,
                                           coal_ramp,
                                           ramp_cap_upward
                                           )).reshape((-1, 3),
                                                      order='F')

    # 新能源0.72%，负荷0.4%设置橙色圈，新能源0.05%、负荷0.06%设置红圈
    orange = 0.0072 * (wind_data + solar_data) + 0.004 * load_data
    red = 0.0005 * (wind_data + solar_data) + 0.0006 * load_data
    orange_minus_red = orange - red
    ramp_cap_upward_threshold = np.stack([red - orange_minus_red,
                                          red,
                                          orange,
                                          orange + orange_minus_red, ],
                                         axis=1)

    ######## 电科院新定义结束 ########

    # 连续运行机组可用爬坡容量
    gen_on_min = np.multiply(gen_state, gen_p_min)
    gen_on_max = np.multiply(gen_state, gen_p_max)
    ramp_cap_downward = np.minimum((gen_output - gen_on_min), ramp_cap_on_gen)
    ramp_cap_downward = ramp_cap_downward.sum(axis=0)
    # 可关机组可用爬坡容量
    greater_than_min_on_hours = (oper_hour >= gen_min_on_hours)
    output_at_p_min_gens = (gen_output == gen_p_min)
    greater_than_min_on_hours_gens = np.multiply(gen_state, greater_than_min_on_hours)
    may_be_turned_off_gens = np.multiply(greater_than_min_on_hours_gens, output_at_p_min_gens)
    may_be_turned_off_gens_cap = np.multiply(may_be_turned_off_gens, gen_p_min)
    # 总可用爬坡容量
    ramp_cap_downward += may_be_turned_off_gens_cap.sum(axis=0)

    return ramp_cap_upward, ramp_cap_upward_comp, ramp_cap_upward_threshold, ramp_cap_downward


def cal_peak_shaving(net, config, res, res_area, area_details, area_name,ptdf=None):
    """
    计算调峰能力,负备用容量;
    """
    num_snaps = config['sum_snap']

    res_rlt_dict = get_device_rlt_powers(net, config, res_area, area_details, area_name)

    load_data = res_rlt_dict['load']  # 负荷
    feedin_data = res_rlt_dict['feedin_output']  # 区外来电
    # gen_on_max = res_rlt_dict['gen_on_max']  # 所有开机机组的装机容量和
    stogen_data = res_rlt_dict['stogen_output']  # 储能充放
    # hydropower_data = res_rlt_dict['hydropower_output']  # 水库水电
    # gen_output = res_rlt_dict['gen_output']  # 机组出力
    wind_data = res_rlt_dict['wind_output']  # 风电出力
    wind_curtail = res_rlt_dict['wind_curtailment']  # 弃风电力
    solar_data = res_rlt_dict['solar_output']  # 光伏出力
    solar_curtail = res_rlt_dict['solar_curtailment']  # 弃光电力

    #### 风光
    renewable = wind_data + solar_data

    if area_name is None:
        # 所有机组的装机容量
        gen_p_max = np.absolute(net.gen.loc[:, 'max_p_mw'].values.reshape(-1, 1))
        # 最小技术出力
        gen_p_min = net.gen.loc[:, 'min_p_mw'].values.reshape(-1, 1)
    else:
        t_rows_gen = area_details[area_name]['gen']  # 分区关联的gen的索引;
        # 所有机组的装机容量
        gen_p_max = np.absolute(net.gen.loc[t_rows_gen, 'max_p_mw'].values.reshape(-1, 1))
        gen_p_min = net.gen.loc[t_rows_gen, 'min_p_mw'].values.reshape(-1, 1)

    # 等效净负荷
    # net_load = load_data - renewable - stogen_data - hydropower_data - feedin_data

    gen_state = res_area['gen_state']

    technical_output_min = (np.multiply(gen_state, gen_p_min)).sum(axis=0)

    # 调峰裕度
    # h_per_day = 24  ## 假设步长为1小时
    # 日内机组处理区间大小
    # daily_gen_max = technical_output_max.reshape((-1, h_per_day)).max(axis=1)
    # daily_gen_min = technical_output_min.reshape((-1, h_per_day)).min(axis=1)
    # daily_gen_diff = daily_gen_max - daily_gen_min
    # 日内等效负荷峰谷差
    # daily_load_max = net_load.reshape((-1, h_per_day)).max(axis=1)
    # daily_load_min = net_load.reshape((-1, h_per_day)).min(axis=1)
    # daily_load_diff = daily_load_max - daily_load_min

    # daily_load_diff_flatten = np.repeat(daily_load_diff[:, np.newaxis], h_per_day, axis=1).reshape((-1,))
    # daily_gen_diff_flatten = np.repeat(daily_gen_diff[:, np.newaxis], h_per_day, axis=1).reshape((-1,))

    # 网损和出力带不足
    dispatchable = gen_p_max.sum() + stogen_data
    loss_and_gen_cut = 0. * dispatchable + 0. * load_data

    # 主变下送能力
    if ptdf is None:
        net_ptdf = make_PTDF_slack(net, select_idx=config['selected_branch_idx'], config=config,
                                   distributed_slack=True)
    else:
        net_ptdf = ptdf

    if area_name is not None:
        # 负荷增量计算法计算分区主变下送容量
        trafo_power = res["trafo_power"]
        load_power = res["load"] + res["load_curtailment"]
        trafo_supply_cap = get_zone_trafo_cap(net, area_name, area_details, trafo_power, load_power, net_ptdf)
        min_dispatchable_plus_curtail = trafo_supply_cap + technical_output_min + renewable + wind_curtail + solar_curtail
    else:
        # 最小电力资源加弃电
        min_dispatchable_plus_curtail = feedin_data + technical_output_min + renewable + wind_curtail + solar_curtail - loss_and_gen_cut - 0

    peak_shaving = load_data - min_dispatchable_plus_curtail
    peak_shaving_comp = np.concatenate((load_data,
                                        min_dispatchable_plus_curtail,
                                        peak_shaving)
                                       ).reshape((-1, 3), order='F')

    peak_shaving_threshold = np.stack([np.array(num_snaps * [0]),
                                       np.array(num_snaps * [1500]),
                                       np.array(num_snaps * [3000]),
                                       np.array(num_snaps * [4500])], axis=1)

    return peak_shaving, peak_shaving_comp, peak_shaving_threshold


def cal_feedin_dependence(config, res_area, area_name):
    """
    计算外来电依存度 = feedin / 总负荷，若是分区,feedin则取分区内主变的功率和;
    """
    num_snaps = config['sum_snap']

    # 负荷
    load_data = np.absolute(res_area['load'].sum(axis=0))
    load_data[load_data < 0.0001] = 0.0001

    # 区外来电依存度
    if area_name is None:  # 全省
        feedin = res_area['feedin_output'].sum(axis=0)
    else:  # 分区
        feedin = np.absolute(res_area['trafo_power'].sum(axis=0))
    feedin_dependence = 10 * (feedin / load_data)  # 此处*10,后续所有数据均会 *0.1转换单位,后恢复;
    feedin_dependence = np.clip(feedin_dependence, 0, 10.)
    # print('外来电依存度', area_name, feedin_dependence.mean()/10)
    feedin_dependence_comp = np.concatenate((feedin,
                                             load_data,
                                             feedin_dependence)).reshape((-1, 3), order='F')
    feedin_dependence_threshold = 10 * np.tile([0.9, 0.6, 0.3, 0], (num_snaps, 1))  # 此处一样

    return feedin_dependence, feedin_dependence_comp, feedin_dependence_threshold


def cal_non_inertia_penetration(config, res_area, area_name):
    """
    计算(新能源)渗透率,无惯量电源 = 新能源 + feedin
    """
    num_snaps = config['sum_snap']

    # 负荷
    load_data = np.absolute(res_area['load'].sum(axis=0))
    load_data[load_data < 0.0001] = 0.0001

    # 区外来电依存度
    if area_name is None:  # 全省
        feedin = res_area['feedin_output'].sum(axis=0)
    else:  # 分区
        feedin = np.absolute(res_area['trafo_power'].sum(axis=0))

    # 风光
    renewable_data, wind_data, solar_data, wind_curtail, solar_curtail = get_renewable_data(res_area, config)

    # 常规机组-开机
    # gen_p_max = np.absolute(net.gen.loc[t_rows_gen, 'max_p_mw'].values.reshape(-1, 1))
    # gen_on_max = np.multiply(res_area['gen_state'].loc[:, t_cols].values, gen_p_max)

    # 无惯量电源渗透率
    non_inertia_penetration = 10 * (wind_data + solar_data + feedin) / load_data  # 此处 *10,在后续所有数据均会 *0.1转换单位,后恢复;
    non_inertia_penetration_comp = np.concatenate((wind_data,
                                                   solar_data,
                                                   feedin,
                                                   load_data,
                                                   non_inertia_penetration)
                                                  ).reshape((-1, 5), order='F')
    non_inertia_penetration_threshold = 10 * np.tile([0.85, 0.6, 0.35, 0.1], (num_snaps, 1))

    return non_inertia_penetration, non_inertia_penetration_comp, non_inertia_penetration_threshold


def get_area_types(prepared_data, net, config, result_dict, area_name, area_details: dict):
    """
    确定分区类型;
    0:'供电资源充裕型'：如果分区供电紧张小时==0 && 供电缺电小时==0
    1:'新能源依赖型'：
    2:'燃机依赖型':
    3:'主变容量不足型':
    4:'主变降压不均型':
    input:
    net: 拓扑网络:
    config: 配置参数;
    result_dict: teap计算结果dict
    prepared_data: 分区相关结果数据;
    area_details: 分区关联设备索引映射;
    """
    # "types_define": {"marginRich": '供电资源充裕型', "renewableRelay": '新能源依赖型', "gasRelay": '燃机依赖型',
    #                  "trafoCapacityLack": '主变容量不足型', "trafounEven": '主变降压不均型'}
    area_types = {}

    psm = prepared_data['power_supply_margin']  # 分区的供电裕度;
    thresh = prepared_data['thresholds']  # 阈值;  # (n * 4)
    metric_comp = prepared_data['metric_comp']  # 计算分区供电裕度的基础数据
    trafo_uti_rate = np.divide(result_dict['trafo_power'],
                               (net.trafo['stable_limit_mw'].values)[:, np.newaxis])  # 所有主变限额利用率
    # 如果有主变断面--优先用主变断面的限额;（trafo_uti_rate）
    # 用主变断面的限额利用率替换更新对应的主变的负载率;
    if "interface_power" in result_dict.keys():
        trafo_ = net.trafo[net.trafo["interface"].notna()]
        trafo_["interface"] = trafo_["interface"].astype(int)
        if len(trafo_["interface"])>0:
            trafo_inf_dict = trafo_.groupby("interface").apply(lambda x: x.index.tolist()).to_dict()
            for inf_idx, trafo_idx in trafo_inf_dict.items():
                inf_maxp_mw = net.interface.loc[inf_idx, "max_p_mw"]
                trafo_uti_rate[trafo_idx] = np.divide(result_dict["interface_power"][inf_idx, :], inf_maxp_mw)
    # 负载率全部取绝对值
    trafo_uti_rate = np.absolute(trafo_uti_rate)

    # 裕度紧张时刻筛选;
    _tm_df = get_time_range(config['start_time'], config['sum_snap'])
    _tm_df['psm'] = psm
    # _tm_df['thresh'] = [thresh[i].tolist() for i in range(len(thresh))]
    # _tm_df['thresh_0'] = thresh[:, 1]   # 供电不足--阈值
    _tm_df['thresh_1'] = thresh[:, 2]  # 供电紧张--阈值
    psm_shortage_tm = _tm_df[_tm_df['psm'] <= _tm_df['thresh_1']]['data'].values.tolist()

    # 新能源依赖型 = 新能源装机/分区总负荷最大值;
    area_wind_idx = area_details[area_name]['wind']
    area_solar_idx = area_details[area_name]['solar']
    area_load_idx = area_details[area_name]['load']
    renewable = net.wind.loc[area_wind_idx, 'max_p_mw'].sum() + net.solar.loc[area_solar_idx, 'max_p_mw'].sum()
    area_load = result_dict['load'][area_load_idx, :].sum(axis=0)
    area_types['renewableRelay'] = (renewable / area_load).max()  # '新能源依赖型'指标

    # 燃机依赖型
    area_gen_idx = np.array(area_details[area_name]['gen'])
    area_gas_mask = np.isin(np.array(area_gen_idx), net.gen[net.gen["type"] == "gas"].index)
    area_gas_idx = area_gen_idx[area_gas_mask]
    area_gas_sum = net.gen.loc[area_gas_idx, "max_p_mw"].sum()
    area_types['gasRelay'] = area_gas_sum / (psm + area_load).max()  # '燃机依赖型'指标

    k = 0.1
    area_trafoall_idx = np.array(area_details[area_name]['trafo'])
    area_alltrafo_grp_kv = min(500, net.trafo.loc[area_trafoall_idx, "vn_hv_kv"].max())
    net_hv_trafo_idx = net.trafo[net.trafo["vn_hv_kv"] >= area_alltrafo_grp_kv * 0.95].index
    area_trafo_mask = np.isin(np.array(area_trafoall_idx), net_hv_trafo_idx)  # 500kV主变或最高电压等级的主变索引
    area_trafo_idx = area_trafoall_idx[area_trafo_mask]

    # 主变容量不足型 # 取500kV主变或最高电压等级的主变容量
    trafo_info = net.trafo.loc[area_trafo_idx, :]  # 分区内500kV主变;
    trafo_rate_area = trafo_uti_rate[area_trafo_idx, :][:, psm_shortage_tm]
    trafo_rate_weighted_avg = np.average(trafo_rate_area, weights=trafo_info['stable_limit_mw'],
                                         axis=0).mean()
    area_types['trafoCapacityLack'] = 0.0 if np.isnan(
        trafo_rate_weighted_avg) else trafo_rate_weighted_avg  # '主变容量不足型' 指标

    # 主变降压不均型
    trafo_cap_sum = k * net.trafo.loc[area_trafo_idx, "stable_limit_mw"].sum()  # 主变限额容量
    trafo_capability = np.array(metric_comp)  # 主变下送容量
    if len(psm_shortage_tm) == 0:
        area_types['trafounEven'] = 0.0
    else:
        trafo_uneven = (trafo_cap_sum - trafo_capability[psm_shortage_tm].max()) / trafo_cap_sum
        area_types['trafounEven'] = 0.0 if np.isnan(trafo_uneven) else trafo_uneven  # '主变降压不均型'指标

    return area_types


def indicator_metrics(net, config, result_dict, area_detail_dict: dict, ptdf=None):
    """
    保供相关的指标整理,并计算分区的六维保供指标;
    result_dict: 全网的分析结果;
    area_detail_dict: 分区信息;
    return:
    self.indicator_rlt_dict['indicator_cal_data']['zone_name']
    """
    sum_snap = config['sum_snap']
    metric_6_names = [
        'power_supply_margin', 'reserve_low', 'peak_shaving',
        'non_inertia_penetration', 'feedin_dependence',
        'ramp_cap_upward']  # 指标: 供电裕度、正备用裕度、负备用容量、无惯量电源渗透率、外来电依存度、爬坡能力
    # 这四个指标只针对全网来说有意义,分区的这四个指标也用全网的指标;--针对分区只有外来电依存度和供电裕度保留;
    # metrics_qs_only = ['reserve_low', 'peak_shaving', 'non_inertia_penetration', 'ramp_cap_upward']
    # metrics_qw_only = config['metrics_all_to_zone']  # 只面向全省/全网的指标--分区的该部分指标用全网指标进行替代;
    # metric_data_qw_only = dict()
    # metric_data_zone_original = dict()  # 分区原始指标数据;
    #
    # boundary_names = ['coal_output', 'gas_output', 'nuclear_output', 'hydro_output',
    #                   'wind_output', 'solar_output', 'storage', 'feedin', 'load']
    boundaries = dict()  # 指标计算的边界数据
    thresholds = dict()  # 指标分级使用的门槛值数据
    indicator_data = dict()  # 指标计算结果的数据
    indicator_cal_comp = dict()  # 保供计算所需的base数据
    area_types = dict()  # 分区能源类型;

    feedin_details = ['feedin_detail_output']  # 如果有直接,加入直流细节，已经是万千瓦

    area_names = list(area_detail_dict.keys())  # 分区列表
    for area_name in [None] + area_names:
        """
        [None] 需要放在最前面,最先进行全网.全省的指标计算,然后计算分区;
        """
        # logger
        print(f"开始分区:{'全网' if area_name is None else area_name}的指标分析计算")

        area_indicators_dict, area_metrics_comp, area_other_metrics = identify_alerts(net, config, result_dict,
                                                                                      area_name, area_detail_dict, ptdf)
        if area_name is None:
            area_name = '全省'

        # 针对面向全省/全网的指标,先提取出全网值，分区值用全网值替代
        # metric_data_fenqu_original, area_metrics_comp, area_other_metrics = replace_area_with_qs_metrics(area_name,
        #                                                                                                  metric_data_qw_only,
        #                                                                                                  metrics_qw_only,
        #                                                                                                  metric_data_zone_original,
        #                                                                                                  area_metrics_comp,
        #                                                                                                  area_other_metrics)
        # 保存 全网/分区的数据到字典
        # 1)全网/分区的指标计算结果数据-> 单位转为万千瓦;
        indicator_data[area_name] = {
            key: clean_standlize((area_indicators_dict[key] * 0.1).round(2), length=sum_snap)
            for key in metric_6_names}

        # 2)全网/分区的指标计算所需要的基础数据--已经合并避免重复-不再按逐个指标存放;
        indicator_cal_comp[area_name] = {key: clean_standlize((area_metrics_comp[key] * 0.1).round(2)) for key in
                                         area_metrics_comp}

        # 3)全网/分区的推演边界数据
        # boundaries[area_name] = {key: clean_standlize(area_other_metrics['boundaries'][key] * 0.1) for key in
        #                          boundary_names}
        boundaries[area_name] = area_other_metrics['boundaries']  # 已经转为万千瓦单位并转为list

        # 4）指标分级门槛边界数值;
        thresholds[area_name] = {
            key: clean_standlize((area_other_metrics['thresholds'][key] * 0.1).round(2)) for key in
            metric_6_names}

        if area_name == '全省':
            for f in feedin_details:
                boundaries[area_name][f] = area_other_metrics['boundaries'][f]
            area_types[area_name] = dict()
        else:
            # 5) 非全省需要;分析分区能源类型
            prepare_area_data = dict()
            prepare_area_data['power_supply_margin'] = area_indicators_dict['power_supply_margin'] * 0.1  # 供电裕度值;
            prepare_area_data['thresholds'] = area_other_metrics['thresholds']['power_supply_margin'] * 0.1  # 阈值
            prepare_area_data['metric_comp'] = area_metrics_comp['psm_0'] * 0.1  # 分区供电裕度计算基础数据-分区主变下送容量
            area_types[area_name] = get_area_types(prepare_area_data, net, config, result_dict, area_name,
                                                   area_detail_dict)
            # 缺电供电紧张时长; 等效缺电时长 = 时序内缺口总电量/最大电力缺口;
    # 推演分析画面中推演计算所需要数据准备---8760的数据;--可以从中提取出推演的输入数据的最大最小值边界;
    ana_cal_boundary_original_dict = get_original_boundaries_for_calc(area_names, boundaries, feedin_details)

    # test:输出所有分区的最小供电裕度;
    # zn_margin_min_df = pd.DataFrame()
    # for zn in area_names:
    #     min_psm = np.array(indicator_data[zn]['power_supply_margin']).min()
    #     zn_margin_min_df[zn] = [min_psm.astype(float)]
    # zn_margin_min_df.to_csv('./zn_margin_min_df.csv', encoding='gbk')

    # 全网/分区的 [zone_name + '全省']：data, 指标计算结果数据、指标计算所需的基础数据、指标分级的门槛数据、原始边界数据--都是8760的数据;
    return indicator_data, indicator_cal_comp, thresholds, ana_cal_boundary_original_dict, area_types


def replace_area_with_qs_metrics(area_name, metric_data_qs_only,
                                 metrics_qs_only,  # old_metric_area,
                                 metric_data_fenqu_original,
                                 metric_comp_area,
                                 other_metrics_area):
    """
    先提取出全网值，如果分区值，分区的指标用全网指标值替代
    """
    if area_name == '全省':
        metric_data_qs_only['metric'] = dict()
        metric_data_qs_only['composition'] = dict()
        metric_data_qs_only['thresholds'] = dict()
        for metric in metrics_qs_only:
            # metric_data_qs_only['metric'][metric] = old_metric_area[metric]
            metric_data_qs_only['composition'][metric] = metric_comp_area[metric]
            metric_data_qs_only['thresholds'][metric] = other_metrics_area['thresholds'][metric]
    else:  # 分区
        for metric in metrics_qs_only:
            # old_metric_area[metric] = metric_data_qs_only['metric'][metric]
            metric_data_fenqu_original[metric] = metric_comp_area[metric]
            metric_comp_area[metric] = metric_data_qs_only['composition'][metric]
            other_metrics_area['thresholds'][metric] = metric_data_qs_only['thresholds'][metric]

    return metric_data_fenqu_original, metric_comp_area, other_metrics_area  # old_metric_area,


def get_original_boundaries_for_calc(area_names, boundaries, feedin_details):
    """
    获取推演需要的全网/分区的边界初始值;
    """

    boundary_names_for_calcu = ['wind_output',
                                'solar_output',
                                'coal_output',
                                'nuclear_output',
                                'hydro_output',
                                'storage',
                                'load',
                                'feedin',
                                'gas_output',
                                'centralized_wind_output',
                                'distributed_wind_output',
                                'centralized_solar_output',
                                'distributed_solar_output',
                                'dcline']
    boundary_for_calcu = dict()
    for area_name in area_names + ['全省']:
        boundary_for_calcu[area_name] = dict()
        for col in boundary_names_for_calcu:
            if col not in boundaries[area_name]:
                continue
            boundary_for_calcu[area_name][col] = boundaries[area_name][col]
            if area_name == '全省':
                for f in feedin_details:
                    boundary_for_calcu[area_name][f] = boundaries[area_name][f]

    return boundary_for_calcu


def get_typical_operations(config, res_dict):
    """
    获取全网值得分析的典型方式时刻序号;
    (1)根据用电负荷; 夏大，夏小，冬大，冬小;
    (2)如果有水电，水电出力最大？？目前可能没有
    (3)如果有新能源，新能源出力最大,风电出力最大,光伏出力最大;
    (3)净负荷最小，净负荷最大(净负荷 = 负荷 - 风 - 光 - feedin)
    春: 3,4,5;夏: 6,7,8月;秋:9,10,11;冬: 12,1,2;
    新能源大发;风电大发;光伏大发;
    Args:
        config:
        res_dict:
    Returns:
    """
    case_start_time = config['start_time']
    cal_start_year = pd.to_datetime(case_start_time).year
    num_snaps = config['sum_snap']
    _tm_df = get_time_range(case_start_time, num_snaps)
    _tm_df['month'] = _tm_df.index.month
    _tm_df['hour'] = _tm_df.index.hour

    #### 风光
    renewable_data, wind_data, solar_data, wind_curtail, solar_curtail = get_renewable_data(res_dict, config)
    _tm_df['renewable'] = renewable_data
    _tm_df['wind_data'] = wind_data
    _tm_df['solar_data'] = solar_data

    ##### 负荷
    load_data = np.absolute(res_dict['load'].sum(axis=0) + res_dict['load_curtailment'].sum(axis=0))
    load_data[load_data < 0.0001] = 0.0001
    _tm_df['load'] = load_data

    typical_operations = dict()  ## 典型方式-字典
    feature_dict = dict()  # 特殊工况字典--{key:"summer_wind_low",value:{"name":"夏季无风","value":[1,2,3]}}

    def tmidxto_no(_t_df, idx):
        """
        将时间索引转为序号
        Args:
            _t_df:
            idx:
        Returns:

        """
        tm_no = int(_t_df.loc[idx, 'data'])
        return tm_no

    # 新能源大发
    typical_operations['renewable_high'] = tmidxto_no(_tm_df, _tm_df['renewable'].idxmax())  # 全年-新能源最大时刻
    typical_operations['wind_high'] = tmidxto_no(_tm_df, _tm_df['wind_data'].idxmax())  # 全年-风电最大时刻
    typical_operations['solar_high'] = tmidxto_no(_tm_df, _tm_df['solar_data'].idxmax())  # 全年-光伏最大时刻

    # 选取夏季最大负荷;
    _tm_summer_df = _tm_df[_tm_df['month'].isin(summer_month)]
    if len(_tm_summer_df) > 0:
        typical_operations['summer_high'] = tmidxto_no(_tm_summer_df, _tm_summer_df['load'].idxmax())  # 夏大--最大负荷
        typical_operations['summer_low'] = tmidxto_no(_tm_summer_df, _tm_summer_df['load'].idxmin())  # 夏小--最小负荷
    else:
        typical_operations['summer_high'] = -1
        typical_operations['summer_low'] = -1

    # 选取冬季
    _tm_winter_df = _tm_df[_tm_df['month'].isin(winter_month)]
    if len(_tm_winter_df) > 0:
        typical_operations['winter_high'] = tmidxto_no(_tm_winter_df, _tm_winter_df['load'].idxmax())  # 冬大--最大负荷
        typical_operations['winter_low'] = tmidxto_no(_tm_winter_df, _tm_winter_df['load'].idxmin())  # 冬小--最小负荷
    else:
        typical_operations['winter_high'] = -1
        typical_operations['winter_low'] = -1

    # 秋汛-xunqi_high/xunqi_low
    _tm_autumn_df = _tm_df[_tm_df['month'].isin(autumn_month)]
    if len(_tm_autumn_df) > 0:
        typical_operations['xunqi_high'] = tmidxto_no(_tm_autumn_df, _tm_autumn_df['load'].idxmax())  # 秋大-秋腰-最大负荷
        typical_operations['xunqi_low'] = tmidxto_no(_tm_autumn_df, _tm_autumn_df['load'].idxmin())  # 秋小-秋腰-最小负荷
    else:
        typical_operations['xunqi_high'] = -1
        typical_operations['xunqi_low'] = -1

    # 春节--5/1--10/1
    _tm_spring_df = _tm_df[_tm_df['month'].isin(spring_month)]
    if len(_tm_spring_df) > 0:
        typical_operations['chunjie'] = tmidxto_no(_tm_spring_df, _tm_spring_df['load'].idxmin())  # 春季-负荷最小时刻;
        typical_operations['chunjie'] = tmidxto_no(_tm_spring_df, _tm_spring_df['load'].idxmin())
    else:
        typical_operations['chunjie'] = -1
        typical_operations['chunjie'] = -1

    # 5/1 国庆;
    time1 = str(case_start_time)
    time2 = f"{cal_start_year}-05-01 00:00:00"
    flag1, hours_delta = get_time_step_hours(time1, time2)

    # if calendar.isleap(cal_start_year):  #闰年
    #     typical_operations['labour_day'] = 121 * 24  # 5/1  # 5/1开始时刻;
    #     typical_operations['national_day'] = 274 * 24  # 10/1
    # else:
    #     typical_operations['labour_day'] = 120 * 24
    #     typical_operations['national_day'] = 273 * 24

    # 特殊工况:
    # 夏季特殊工况
    def tmseries_no(_t_df, idx):
        """
        将时间索引转为序号
        Args:
            _t_df:
            idx:
        Returns:

        """
        tm_no_list = (_t_df.loc[idx, 'data'].values).tolist()
        return tm_no_list

    summer_wind_low = _tm_summer_df.sort_values('wind_data').head(5).index.tolist()  # 升序--夏季--极热无风
    feature_dict['summer_wind_low'] = {'name': '极热无风', 'idxes': tmseries_no(_tm_summer_df, summer_wind_low)}

    _tm_summer_daytime_df = _tm_summer_df[_tm_summer_df['hour'].isin(daytime_hours)]  # 在白天
    summer_solar_low = _tm_summer_daytime_df.sort_values('solar_data').head(5).index.tolist()  # 升序--夏季--极热无光
    feature_dict['summer_solar_low'] = {'name': '极热无光',
                                        'idxes': tmseries_no(_tm_summer_daytime_df, summer_solar_low)}

    # 冬季特殊工况
    winter_wind_low = _tm_winter_df.sort_values('wind_data').head(5).index.tolist()  # 升序--冬季--极冷无风
    feature_dict['winter_wind_low'] = {'name': '极冷无风', 'idxes': tmseries_no(_tm_winter_df, winter_wind_low)}

    _tm_winter_daytime_df = _tm_winter_df[_tm_winter_df['hour'].isin(daytime_hours)]  # 在白天
    winter_solar_low = _tm_winter_daytime_df.sort_values('solar_data').head(5).index.tolist()  # 升序--冬季--极冷无光
    feature_dict['winter_solar_low'] = {'name': '极冷无光',
                                        'idxes': tmseries_no(_tm_winter_daytime_df, winter_solar_low)}

    # 有调峰困难

    # 供电缺口

    return typical_operations, feature_dict

# def get_feature_list(config, res_dict, boundaries):
#     """
#     获取特殊工况: 夏季无风/夏季白天无光/冬季无风/冬季白天无光
#     """
#     ##### 负荷
#     load_data = np.absolute(res_dict['load'].sum(axis=0) + res_dict['load_curtailment'].sum(axis=0))
#     load_data[load_data < 0.0001] = 0.0001
#
#     # # 678月
#     # summer_cutoff = [24 * (31 + 28 + 31 + 30 + 31), 24 * (31 + 28 + 31 + 30 + 31 + 30 + 31 + 31)]
#     # # 12月1月2月
#     # winter_cutoff = [24 * (31 + 28), 8760 - 24 * 31]
#     #
#     # ## 特色方式提取
#     # feature_dict = dict()
#     # component_min = dict()
#     #
#     # ## 高负荷
#     # load_high_index = np.argsort(-load_data, axis=0)
#     # load_high_summer = np.logical_and(load_high_index > summer_cutoff[0],
#     #                                   load_high_index < summer_cutoff[1])
#     # load_high_winter = np.logical_or(load_high_index < winter_cutoff[0],
#     #                                  load_high_index > winter_cutoff[1])
#     #
#     # summer_high = load_high_index[load_high_summer]
#     # winter_high = load_high_index[load_high_winter]
#     #
#     # ## 低负荷
#     # load_low_index = np.argsort(load_data, axis=0)
#     # load_low_summer = np.logical_and(load_low_index > summer_cutoff[0],
#     #                                  load_low_index < summer_cutoff[1])
#     # load_low_winter = np.logical_or(load_low_index < winter_cutoff[0],
#     #                                 load_low_index > winter_cutoff[1])
#     # summer_low = load_low_index[load_low_summer]
#     # winter_low = load_low_index[load_low_winter]
#     #
#     # component_min['wind'] = np.argsort(boundaries['wind_output'], axis=0)
#     # component_min['load'] = np.argsort(-boundaries['load'], axis=0)
#     # component_min['solar'] = np.argsort(boundaries['solar_output'], axis=0)
#     # component_min['coal'] = np.argsort(-boundaries['coal_output'], axis=0)
#     # component_min['gas'] = np.argsort(-boundaries['gas_output'], axis=0)
#     #
#     # feature_dict['极热无风'] = [int(w) for w in component_min['wind'][:300] if w in summer_high[:300]]
#     # feature_dict['极冷无风'] = [int(w) for w in component_min['wind'][:300] if w in winter_high[:300]]
#     # feature_dict['极热无光'] = [int(w) for w in component_min['solar'][:300] if w in summer_high[:300]]
#     # feature_dict['极冷无光'] = [int(w) for w in component_min['solar'][:300] if w in winter_high[:300]]
#     # feature_dict['夏谷大风'] = [int(w) for w in component_min['wind'][-300:] if w in summer_low[:300]]
#     # feature_dict['冬谷大风'] = [int(w) for w in component_min['wind'][-300:] if w in winter_low[:300]]
#
#     if config['ss_config']['bus_name_startswith'] == '苏':
#         # 江苏假数据
#         feature_dict = {
#             ' 极热无风': [4649, 4672],
#             ' 极冷无风': [81, 8444, 8467, 8539, 8660, 8636],
#             ' 极热无光': [4632, 4653, 4677],
#             ' 极冷无光': [44],
#             ' 汛低新能源大发': [6324, 6396, 6420, 6564, 6584, 6925, 6996],
#             ' 冬低大风': [593, 864],
#             ' 冬高新能源大发': [8589],
#             ' 龙政检修': [7671],
#             ' 直流来电不及预期': [5204, 5272, 5277, 5293, 5300],
#             ' 有序用电': [4699],
#             ' 冬季早峰新能源大发': [8530],
#         }
#     else:
#         # 河南假数据
#         feature_dict = {
#             ' 夏季高峰': [4294],
#             ' 夏季低谷': [5621],
#             ' 冬季高峰': [164],
#             ' 冬季低谷': [7950],
#             ' 春节': [1118],
#         }
#     return feature_dict
