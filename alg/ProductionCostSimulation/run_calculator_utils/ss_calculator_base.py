"""
#获取推演分析计算的基础的数据
"""
import copy
import math
import pandas as pd
import numpy as np
from itertools import chain
from alg.conf.log_conf import logger
import alg.cal_ssmetrics.base_func as basef
from pandapower.run import rundcpp, set_user_pf_options
from alg.ProductionCostSimulation.utils.make_PTDF import (make_PTDF, make_PTDF_LODF, get_slack_gen)
from alg.ProductionCostSimulation.utils.ptdf_util import (cal_branch_rate_simple, get_Pbr_max,
                                                          prepare_net_with_new_device,
                                                          add_reactance, cal_branch_power_add, filter_branch_idx,
                                                          df_to_json)
from alg.get_graph_info.get_net_topo import (select_available_device, get_graph_data)
from alg.cal_ssmetrics.supply_index_cal import get_above_vn_kv_trafoidx, get_zone_trafo_cap


def norm_and_clip(a, a_max):
    b = a / a_max
    b = np.clip(b, 0., 1.)
    b = np.nan_to_num(b)

    return b


def calc_area_level_psm(net, result_dict, config: dict = dict(), user_input=None,
                        power_delta_qs=None,
                        metric_comp_areas=None,
                        b_original_areas=None,
                        thresholds_areas=None,
                        area_details=None,
                        gen_list_dict=None,
                        branch_power=None,  # new_power
                        ptdf=None,  # ptdf
                        ):
    """
    推演分析时,重新计算全网的分区的供电裕度
    Args:
        net:
        result_dict:
        config:
        power_delta_qs:
        user_input: 用户输入的边界数据
        metric_comp_areas:
        b_original_areas:  所有分区的初始出力边界数据
        thresholds_areas:  对应时刻的边界数据;
        area_details:  分区设备索引
        gen_list_dict=None,
        branch_power=None,  # new_power
        ptdf=None,  # ptdf
    Returns:
        ctl-20240514,增加graph_data及 主变线路断面的负载率情况输出
    """
    timestep = user_input["timestep"]
    user_input_dcfeedin = user_input['dc_feedin']  # 用户输入的dcfeedin的调整细节数据;
    user_input_gen = user_input['gen_output'] if ('gen_output' in user_input.keys()) else None  # 用户输入的机组调整;'target'

    # 边界数据变化量
    feedin_delta_qs = power_delta_qs['feedin']  # feedin是全部feedin的变化量(包含直流总调节量)
    load_delta_qs = power_delta_qs['load']
    wind_delta_qs = power_delta_qs['wind_output']
    solar_delta_qs = power_delta_qs['solar_output']
    gen_delta_qs = power_delta_qs['gen_output']
    # feedin_details_delta_qs = power_delta_qs['dc_feedin']  # 直流细节调节量--按单设备调节;

    # 原电网整体出力/负荷边界
    # feedin_original_qs = b_original_areas['全省']['feedin'][timestep]
    # feedin_original_qs = 0.1 if abs(feedin_original_qs) < 0.1 else feedin_original_qs
    load_original_qs = b_original_areas['全省']['load'][timestep]
    wind_original_qs = b_original_areas['全省']['wind_output'][timestep]
    wind_original_qs = 0.1 if wind_original_qs < 0.1 else wind_original_qs
    solar_original_qs = b_original_areas['全省']['solar_output'][timestep]
    solar_original_qs = 0.1 if solar_original_qs < 0.1 else solar_original_qs
    gen_original_qs = b_original_areas['全省']['gas_output'][timestep]  # 原总燃机出力
    gen_original_qs = 0.1 if gen_original_qs < 0.1 else gen_original_qs

    # 全网的feedin初值的边界;

    # 所有分区名
    area_names = list(metric_comp_areas.keys())

    # ####### 机组调整量在供电分区的分配 ########
    if user_input_gen is not None and 'area_or_region_choice' in user_input_gen and 'gen_choices' in user_input_gen:
        # 用户输入的选项user_input_gen：
        # 选择的全省、区域或分区'area_or_region_choice'；
        # 选择的机组列表'gen_choices'；
        # 目标总出力'target';
        # 总调整量
        gen_delta_inchosen = power_delta_qs['gen_output']

        # 如果没有在分区层面选择机组，则需要把机组出力调整量分配到分区
        gen_delta_area = {}
        if user_input_gen['area_or_region_choice'] in ['全省']:  # ['全省', '苏北', '苏南']
            # 找到发生调整的机组所在的分区
            area_with_chosen_gen = []
            for gen in user_input_gen['gen_choices']:
                area_with_chosen_gen.append(next(k for k, v in gen_list_dict.items() if gen in v))
            # 把调整量分配到分区(最小供电分区)
            gen_delta_area = {a: gen_delta_inchosen / len(area_with_chosen_gen) for a in
                              area_with_chosen_gen}  # 涉及到的分区均分机组调整量;
        # 如果在分区层面选择了机组，则调整量都在选择的分区
        else:
            gen_delta_area[user_input_gen['area_or_region_choice']] = gen_delta_inchosen
        # 最后把上述判断没有涉及到的分区的调整量设为0
        for area_name in area_names:
            if area_name not in ['全省']:  # ['全省', '苏北', '苏南']
                if area_name not in gen_delta_area.keys():
                    gen_delta_area[area_name] = 0.
        # print('分区调整量', gen_delta_area)
    else:
        # 所有分区的机组均无需调整
        gen_delta_area = {area_name: 0. for area_name in area_names}
    # ####### 结束机组调整量在分区层面的分配 ########

    # 获取net和PTDF--网架不变;ptdf不变
    result_df_dict = result_to_df_dict(net, config, result_dict)  # 将result_dict恢复成初始带name和Pbr_max值的结构
    # ptdf = make_PTDF(net, select_idx=config['selected_branch_idx'], config=config,
    #                  distributed_slack=True)  # ptdf dict--not use:make_PTDF_dict
    # 做一次潮流计算，重新获取支路潮流数据和主变下送能力，参与计算裕度
    # 准备潮流计算的结果
    # 此处跟旧程序做更新(n_1_dummy_input),因为需要获取设备负载率潮流不仅仅计算供电裕度,需要传入整个边界的调整情况;和计算潮流一样;
    # 重新计算潮流;
    # ptdf, graph_dict, ret_dict, branch_power = cal_device_rate(net, result_dict, config,
    #                                                            user_input_init=user_input_data,
    #                                                            # n_1_dummy_input从调整为用户输入的数据边界;
    #                                                            target_time=timestep,
    #                                                            device_name=grid_fault_device_name,
    #                                                            phase_shifter_arg=phase_shifter_arg,
    #                                                            add_reactance_arg=add_reactance_arg,
    #                                                            area_name=None,  # None就是全省,
    #                                                            area_details=area_details
    #                                                            )
    num_lines = len(net.line.index)
    # 计算全网/分区的供电裕度指标结果;---分区按比例分配delta
    area_psm_value = dict()  # 分区供电裕度dict
    area_psm_color = dict()  # 分区供电裕度着色
    area_trafo_value = dict()  # 分区主变dict
    trafo_power_with_index = dict()
    for area_name in area_names:
        if area_name in ['全省']:  # 全网不额外重新计算指标;
            continue
        # 按比例计算出分区分配的delta
        load_prop = b_original_areas[area_name]['load'][timestep] / load_original_qs  # 分区负荷占全网负荷比例
        wind_prop = b_original_areas[area_name]['wind_output'][timestep] / wind_original_qs
        solar_prop = b_original_areas[area_name]['solar_output'][timestep] / solar_original_qs
        if np.isnan(solar_prop):  # 若此时刻光伏全为0，则比例用容量代替
            area_solar_idx = area_details[area_name]['solar']  # 分区关联的光伏索引;
            if len(area_solar_idx) == 0:
                solar_prop = 0
            else:
                area_solar_cap = net.solar.loc[area_solar_idx, 'max_p_mw'].sum()
                solar_prop = area_solar_cap / net.solar['max_p_mw'].sum()
                if np.isnan(solar_prop):
                    solar_prop = 0
        gen_prop = b_original_areas[area_name]['gas_output'][timestep] / gen_original_qs  # gas的出力占比

        # 总外来电、负荷、风光、机组的变化量都按比例摊到负荷
        if user_input_dcfeedin != {}:  # 全省层面调整了细节直流
            load_delta_ = load_delta_qs * load_prop
        else:  # 全省层面没有调整细节直流
            load_delta_ = (load_delta_qs - feedin_delta_qs) * load_prop
        wind_delta_ = wind_delta_qs * wind_prop
        solar_delta_ = solar_delta_qs * solar_prop
        if np.isnan(solar_delta_):
            solar_delta_ = 0
        gen_delta_ = gen_delta_qs * gen_prop

        # 如果发生了分区层面机组，则替换掉
        gen_delta_ = gen_delta_area[area_name] if user_input_gen is not None else gen_delta_

        # 重新获取主变下送能力，选取500kV及以上主变(同供电裕度计算时的主变选择)
        num_lines = len(net.line.index)
        tf_power = branch_power[num_lines:].squeeze()
        load_power = result_dict["load"][:, timestep] + result_dict["load_curtailment"][:, timestep]
        trafo_cap_cal = cal_trafo_capability(net, area_name, area_details, tf_power, load_power, ptdf)
        trafo_cap_cal *= 0.1  # 万千瓦

        # 汇总计算
        psm = metric_comp_areas[area_name]['power_supply_margin'][timestep]
        # 主变初始的下送能力值
        trafo_cap_delta = trafo_cap_cal - psm[0]
        psm_ = (psm[0] + trafo_cap_delta) + (
                psm[1] + gen_delta_) + (
                       psm[2] + wind_delta_ + solar_delta_) - (
                       psm[3] + load_delta_)
        area_psm_value[area_name] = round(psm_, 1)

        # 着色
        area_thresholds = thresholds_areas[area_name]['power_supply_margin'][timestep]
        abs_threshold = np.array(area_thresholds)[1:3]
        psm_cm_bool = np.where(psm_ > abs_threshold, 1, 0)
        area_psm_color[area_name] = psm_cm_bool.sum(0).tolist()

        # 若有细节直流，加一个返回值：各分区的区外受电
        area_trafo_value[area_name] = psm[0] + trafo_cap_delta

        # 主变降压功率细节(500kV及以上主变)
        area_trafo_idx_all = np.array(area_details[area_name]['trafo'])
        area_trafo_selected_idex = get_above_vn_kv_trafoidx(net)
        area_trafo_idx = area_trafo_idx_all[np.in1d(area_trafo_idx_all, area_trafo_selected_idex)]
        if len(area_trafo_idx) == 0:
            # 找分区内的变压器的最高等级的索引
            vn_kv_zone = net.trafo.loc[area_details[area_name]['trafo'], 'vn_hv_kv'].max()
            area_trafo_idex_new = get_above_vn_kv_trafoidx(net, vn_kv=vn_kv_zone)
            area_trafo_idx = area_trafo_idx_all[np.in1d(area_trafo_idx_all, area_trafo_idex_new)]
        power = branch_power[np.array(area_trafo_idx) + num_lines, :].squeeze()
        trafo_power_with_index[area_name] = df_to_json(
            pd.Series(data=power, index=net.trafo.loc[area_trafo_idx, 'name'].values),
            with_index=True)
    return area_psm_value, area_psm_color, area_trafo_value, trafo_power_with_index


def simu_indicators_cal(chushizhi_timestep, area_name, power_delta, metric_comp_area: dict, timestep,
                        config: dict = dict()):
    """
    根据初始indictor及计算基础数据、用户输入的偏差数据,计算新的指标值
    Args:
        chushizhi_timestep； 全网/分区指标初始值
        area_name:  分区名;
        power_delta:   用户输入的偏差
        metric_comp_area:   基础数据
    Returns:
        dict(),new_indicators_dict-
    """
    if config:
        wind_reserve_cof = config['wind_reserve_cof']
        solar_reserve_cof = config['wind_reserve_cof']
    else:
        wind_reserve_cof = 0.0
        solar_reserve_cof = 0.0

    # 提取原初始场景的指标计算基础数据
    # psm = metric_comp_area['power_supply_margin']
    # ramp = metric_comp_area['ramp_cap_upward'][timestep]
    # dependence = metric_comp_area['feedin_dependence'][timestep]
    # inertia = metric_comp_area['non_inertia_penetration'][timestep]
    # shaving = metric_comp_area['peak_shaving'][timestep]
    # reserve = metric_comp_area['reserve_low'][timestep]
    psm_0_init_area_t = metric_comp_area['psm_0'][timestep]
    dispathable_init_area_t = metric_comp_area['dispathable'][timestep]
    wind_init_area_t = metric_comp_area['wind'][timestep]
    solar_init_area_t = metric_comp_area['solar'][timestep]
    renewable_init_area_t = wind_init_area_t + solar_init_area_t
    load_init_area_t = metric_comp_area['load'][timestep]
    gen_onmax_init_area_t = metric_comp_area['gen_on_max'][timestep]
    gen_p_init_area_t = metric_comp_area['gen_output'][timestep]
    gas_onmax_init_area_t = metric_comp_area['gas_on_max'][timestep]
    coal_onmax_init_area_t = metric_comp_area['coal_on_max'][timestep]
    dependence_feedin_init_area_t = metric_comp_area['feedin_dependence_0'][timestep]
    min_dispatchable_plus_curtail_init_area_t = metric_comp_area['peak_shaving_1'][timestep]

    # 获取6维指标计算基础数据
    ori_index_base_dict = dict()  # 初始基础数据
    psm = [psm_0_init_area_t, dispathable_init_area_t, renewable_init_area_t, load_init_area_t, 0, 0,
           chushizhi_timestep[area_name]['power_supply_margin']]  # 5列--全网
    ramp = [gas_onmax_init_area_t * 0.03, coal_onmax_init_area_t * 0.015,
            chushizhi_timestep[area_name]['ramp_cap_upward']]
    dependence = [dependence_feedin_init_area_t, load_init_area_t,
                  chushizhi_timestep[area_name]['feedin_dependence']]
    inertia = [wind_init_area_t, solar_init_area_t, dependence_feedin_init_area_t, load_init_area_t,
               chushizhi_timestep[area_name]['non_inertia_penetration']]
    shaving = [load_init_area_t, min_dispatchable_plus_curtail_init_area_t,
               chushizhi_timestep[area_name]['peak_shaving']]
    reserve = [gen_onmax_init_area_t - gen_p_init_area_t, wind_reserve_cof * wind_init_area_t,
               solar_reserve_cof * solar_init_area_t, 0.05 * load_init_area_t,
               chushizhi_timestep[area_name]['reserve_low']]
    ori_index_base_dict['power_supply_margin'] = psm
    ori_index_base_dict['ramp_cap_upward'] = ramp
    ori_index_base_dict['feedin_dependence'] = dependence
    ori_index_base_dict['non_inertia_penetration'] = inertia
    ori_index_base_dict['peak_shaving'] = shaving
    ori_index_base_dict['reserve_low'] = reserve
    # 原分析保供指标的全年最大值: 供电裕度、爬坡能力、外来电依存度、无惯量电源依存度、负备用裕度、正备用裕度
    # psm_max = np.array(metric_comp_area['power_supply_margin'])[:, -1].max()
    # ramp_max = np.array(metric_comp_area['ramp_cap_upward'])[:, -1].max()
    # dependence_max = np.array(metric_comp_area['feedin_dependence'])[:, -1].max()
    # inertia_max = np.array(metric_comp_area['non_inertia_penetration'])[:, -1].max()
    # shaving_max = np.array(metric_comp_area['peak_shaving'])[:, -1].max()
    # reserve_max = np.array(metric_comp_area['reserve_low'])[:, -1].max()
    # 计算新的裕度保供指标数据
    feedin_delta = power_delta['feedin']
    load_delta = power_delta['load']
    wind_delta = power_delta['wind_output']
    solar_delta = power_delta['solar_output']
    gen_delta = power_delta['gen_output']

    # 1、新的供电裕度
    delta_psm = feedin_delta + gen_delta + wind_delta + solar_delta - load_delta  # 指标变化值;
    psm_ = psm[-1] + delta_psm
    # psm_n = norm_and_clip(psm_, psm_max)
    # 2、新的爬坡能力值--算的不读,负荷\新能源出力变了,机组出力也会跟着变;
    # TODO: 此处个人感觉算的不对;gen_delta的变化量应该有两部分: 1:用户输入的调节量;2:用户输入风光feedin和负荷后为了功率平衡被动的调节量(先认为是煤机调节)
    # delta_ramp = 0.03 * gen_delta + 0.15 * (load_delta - (feedin_delta + wind_delta + solar_delta) - gen_delta)
    delta_ramp = 0.03 * gen_delta
    ramp_ = ramp[-1] + delta_ramp
    # ramp_n = norm_and_clip(ramp_, ramp_max)
    # 3、新的外来电依存度
    dependence_ = (dependence[0] + feedin_delta) / (dependence[1] + load_delta)
    # dependence_n = norm_and_clip(dependence_, dependence_max)
    # 4、新的无惯量电源渗透率
    inertia_ = ((inertia[0] + wind_delta) +
                (inertia[1] + solar_delta) +
                (inertia[2] + feedin_delta)) / (inertia[3] + load_delta)
    # inertia_n = norm_and_clip(inertia_, inertia_max)
    # 5、新的调峰能力(负备用容量)
    delta_shaving = load_delta - wind_delta - solar_delta
    shaving_ = shaving[-1] + delta_shaving
    # shaving_n = norm_and_clip(shaving_, shaving_max)
    # 6、新的正备用容量--reserve_分区也用全省数值
    delta_reserve = (-1) * (load_delta - wind_delta - solar_delta - feedin_delta)
    # reserve_ = reserve[-1] - gen_delta - (0.05 * wind_delta + 0.05 * solar_delta) - 0.05 * load_delta
    reserve_ = reserve[-1] + delta_reserve
    # reserve_不能大于供电裕度;
    if area_name == '全省':
        reserve_ = np.clip(reserve_, 0, psm_) if psm_ > 0 else 0
    # reserve_n = norm_and_clip(reserve_, reserve_max)
    # 保存指标信息返回
    output_nomimal = {'power_supply_margin': round(psm_, 1), 'ramp_cap_upward': round(ramp_, 1),
                      'feedin_dependence': round(dependence_, 4), 'non_inertia_penetration': round(inertia_, 4),
                      'peak_shaving': round(shaving_, 1), 'reserve_low': round(reserve_, 1)}
    return ori_index_base_dict, output_nomimal


def result_to_df_dict(net, config, result_dict):
    """
    用于生成结果的df的dict
    Args:
        net:案例
        config:配置
        result_dict:文件保存路径
    Returns:
        result_dict:保存的文件名
    """
    _not_output = [
        # 'oper_hour',
        # 'energy_storage',
    ]

    # 保存用于断面筛选的数据
    df_output_dict = dict()

    # 输出发电、储能和区外
    for key, value in config['element_type'].items():
        if key in ["reservoir", "storage"]:
            for _output in value:
                if _output not in config['data_type']:
                    continue
                df_output_dict[_output] = pd.merge(net[key][['name', 'type']], pd.DataFrame(result_dict[_output]),
                                                   left_index=True, right_index=True)
        elif key == "branch":
            # print("config['selected_line_idx']", config['selected_line_idx'])
            line_idx = config['selected_line_idx']
            trafo_idx = config['selected_trafo_idx']
            n_line_output = len(line_idx)
            n_trafo_output = len(trafo_idx)
            net.line['Pbr_max'] = 0
            net.trafo['Pbr_max'] = 0
            Pbr_max = get_Pbr_max(net, line_idx, trafo_idx)

            if n_line_output > 0:
                net.line.loc[line_idx, 'Pbr_max'] = Pbr_max[0:n_line_output, :]
                if 'branch_power' in result_dict:
                    line_power = pd.DataFrame(result_dict['branch_power'][0:n_line_output, :],
                                              index=line_idx)
                else:
                    line_power = pd.DataFrame(result_dict['line_power'], index=line_idx)
                df_output_dict['line_power'] = net.line.loc[
                    line_idx, ['name', 'from_bus', 'to_bus', 'Pbr_max']].join(line_power)
                # df_output_dict['line_power'].to_excel(writer, sheet_name='line_power')
            if n_trafo_output > 0:
                net.trafo.loc[trafo_idx, 'Pbr_max'] = Pbr_max[n_line_output:, :]
                if 'branch_power' in result_dict:
                    trafo_power = pd.DataFrame(result_dict['branch_power'][n_line_output:, :],
                                               index=trafo_idx)
                else:
                    trafo_power = pd.DataFrame(result_dict['trafo_power'], index=trafo_idx)
                df_output_dict['trafo_power'] = net.trafo.loc[
                    trafo_idx, ['name', 'hv_bus', 'lv_bus', 'Pbr_max']].join(trafo_power)
                # df_output_dict['trafo_power'].to_excel(writer, sheet_name='trafo_power')
        elif key == "interface":
            if "interface_power" in result_dict:
                interface_power = pd.DataFrame(result_dict['interface_power'])
                interface = net.interface.loc[:, ['name', 'cap_const', 'max_p_mw', 'min_p_mw']].join(
                    interface_power)
                if len(interface[interface["cap_const"]]) > 0:
                    df_output_dict["interface_power"] = interface[interface["cap_const"]]
                # df_output_dict[_output].to_excel(writer, sheet_name='interface_power')
        elif key == "dcline":
            for _output in value:
                if _output not in config['data_type']:
                    continue
                df_output_dict[_output] = pd.merge(net[key][["name", "from_bus", "to_bus", "max_p_mw", "min_p_mw"]],
                                                   pd.DataFrame(result_dict[_output]),
                                                   left_index=True, right_index=True)
        else:
            for _output in value:
                if _output not in config['data_type']:
                    continue
                branch_power = pd.DataFrame(result_dict[_output])
                df_output_dict[_output] = net[key][['name', 'bus']].join(branch_power)
    return df_output_dict


def cal_device_rate(net, result_dict, config, user_input_init, target_time, power_delta: dict = dict(),
                    area_name=None, area_details: dict = dict()):
    """
    计算设备负载率 推演
    获取基本的分析数据，包括基本数据和推演变化量；计算新的支路潮流分布；计算新的设备负载率
    # 如果没有移相器/容抗器，并没有进行潮流计算，直接使用ptdf结果进行折算;
    目前分配原则，变化量按照当前出力，分摊给各个设备；出现问题时，按照装机容量分摊
    feedin的调节量有两部分:1)整体;2)单设备;总调节量=1）+2）
    area_name=None,表示全省
    Args:
        net:
        result_dict:
        config:
        user_input_init:
        target_time: 推演仿真的时刻;
        power_delta: 边界变化值;
        area_name:
        area_details:  分区关联设备索引明细;
    Returns:
    """
    device_name = user_input_init['device_name']  # N-1设备
    phase_shifter_arg = user_input_init['phase_shifter_arg']  # 移相器
    add_reactance_arg = user_input_init['add_reactance_arg']  # 新增电抗器;

    net_c = copy.deepcopy(net)

    ##### 获取完整网架的lodf
    # ptdf, lodf = make_PTDF_LODF(net_c, select_idx=config['selected_branch_idx'], config=config, distributed_slack=False)

    ##### 获取支路限额
    # Pbr_max = get_Pbr_max(net_c, net_c.line.index, net_c.trafo.index)

    ##### 获取全省各边界(设备)在net中编号--如果是全网所有设备索引都是空
    area_inds = get_area_indices(area_name=None, area_details=area_details)

    ##### 计算/获取各部分增量(feedin,feedin支持单设备调节推演,此函数中为feedin调节总量)
    # user_input_delta = cal_delta(net_c, result_dict, target_time, user_input_init,
    #                              area_inds)
    bd_name_dict = {'wind_output': "wind", "solar_output": "solar", "load": "load", "feedin": "feedin",
                    "gas_output": "gas"}
    user_input_delta = dict()
    for key, val in bd_name_dict.items():
        user_input_delta[val] = power_delta[key] * 10  # 万千瓦->MW;
    if "dcline" in power_delta and power_delta["dcline"]:
        user_input_delta["dcline"] = power_delta["dcline"]

    # 计算精细化各设备的调节量(feedin,具体到设备,返回的是(m,1)
    user_input_delta["dc_feedin"] = cal_feedin_dev_delta(power_delta, config=config)
    ##### 直流潮流计算；如果有故障，这里会返回一个简化容抗以后的lodf，否则返回原lodf
    branch_power, Pbr_max, ptdf, lodf = cal_pf(net_c, config, result_dict,
                                               user_input_init, user_input_delta,
                                               device_name, target_time, area_inds,
                                               phase_shifter_arg=phase_shifter_arg,
                                               add_reactance_arg=add_reactance_arg)

    ##### 指标计算 重点保障通道 #####
    # lines_vip = vip_lines_N_1_1(net, branch_power, lodf, Pbr_max)

    ##### 指标计算 地图数据准备 #####
    graph_dict, _ret_dict = get_graph_dict(net_c, config, result_dict, branch_power,
                                           Pbr_max, device_name, target_time, area_name=area_name,
                                           area_details=dict(**area_details))

    ##### 指标计算 计算设备负载率(推演的饼图和柱图) #####
    # ret_dict = cal_branch_rate(net, config, branch_power, Pbr_max)
    ret_dict = cal_branch_rate_simple(net_c, config, branch_power, Pbr_max, area_name, area_details)

    ##### 重点保障通道结果、利用率大幅变化设备结果，装入 ret_dict
    # ret_dict['vip_lines'] = lines_vip
    # ret_dict = {**ret_dict, **_ret_dict}

    return ptdf, graph_dict, ret_dict, branch_power

def cal_trafo_capability(net, area_name, area_details, trafo_power, load_power, ptdf):
    # ----直接调用指标计算中的模块--
    tf_power = trafo_power.reshape(-1, 1)  # (n,)转(n,1)
    load_c = load_power.reshape(-1, 1)
    trafo_supply_cap = get_zone_trafo_cap(net, area_name, area_details, tf_power, load_c, ptdf)
    # 单位是MW
    return trafo_supply_cap[0]

def get_trafo_details(net, result_dict,
                      area_details: dict,
                      area_name,
                      timestep=100,
                      user_input_area=None,
                      branch_power=None,  # new_power
                      ptdf=None,  # ptdf
                      ):
    """返回指定分区的主变下送功率等细节
    Args:
        net: 网络;
        result_dict: 原始计算结果arr-dict
        area_details: 分区关联设备索引
        area_name (str, optional): _description_. Defaults to '茅溧'.
        timestep (int, optional): _description_. Defaults to 100.
        # target_trafo_name (_type_, optional): _description_. Defaults to None.
        user_input_area (_type_, optional): _description_. Defaults to None.

    Returns:
        _type_: _description_
    """

    # 获取分区主变参数和主变潮流
    area_bus_idx = area_details[area_name]['bus']
    area_bus = net.bus.loc[area_bus_idx, :]
    area_load_idx = area_details[area_name]['load']
    area_load = net.load.loc[area_load_idx, :]
    area_trafo_idx = area_details[area_name]['trafo']
    area_trafo = net.trafo.loc[area_trafo_idx, :].copy()

    # 只考虑500kV或最高电压等级的的主变--'hv_bus' max
    trafo_hv_vn_kv = area_trafo['vn_hv_kv'].max()
    trafo_hv_vn_kv = min(trafo_hv_vn_kv, 500)
    area_trafo = area_trafo.loc[area_trafo['vn_hv_kv'] >= trafo_hv_vn_kv * 0.95, :]

    area_trafo = area_trafo.merge(area_bus, left_on='hv_bus', right_index=True)
    area_trafo = area_trafo.sort_index(axis=0)
    trafo_names_unique = area_trafo['dispname'].unique()
    trafo_names_unique = [name for name in trafo_names_unique if not pd.isna(name)]  # 去除nan值
    trafo_names_unique = [name for name in trafo_names_unique if not ""]  # 去除""值
    trafos_with_name = dict()
    for name in trafo_names_unique:
        trafos_with_name[name] = area_trafo.loc[area_trafo['dispname'] == name, :]

    # 线路主变信息提取
    # trafo_power_all = pd.DataFrame(result_dict['trafo_power'][:, timestep])
    num_lines = result_dict['line_power'].shape[0]

    logger.info('get_trafo_details has started.')
    # 如果指定了故障设备 or 加移相器 or 加串抗，则准备参数，做一次潮流计算
    # 准备N-1参数
    device_name = user_input_area['device_name']

    logger.info(f'grid fault device_name is {device_name}')

    output = dict()
    for k, v in trafos_with_name.items():
        # print('======开始测算新主变负载', k, '======')
        power = pd.Series(index=v.index, data=branch_power[v.index + num_lines, :].squeeze())
        rate = power / v['stable_limit_mw']
        # logger.info(f'original trafo: {v}')
        # logger.info(f'original trafo rate: {rate}')
        # logger.info(f'original trafo power: {power}')
        output[k] = dict()
        output[k]['lat'] = float(v['lat'].values[0])
        output[k]['lon'] = float(v['lon'].values[0])
        output[k]['load_ratio'] = list(rate.values)
        output[k]['distri_limit'] = list(v['stable_limit_mw'].values / 10)  # '分台运行限额(万千瓦)' ---'distri_limit'

        if len(device_name) > 0:
            limit = float(v.loc[v.index != (device_name[0] - num_lines), 'stable_limit_mw'].sum() / 10)
        else:
            limit = float(v['stable_limit_mw'].sum() / 10)

        output[k]['total_limit'] = limit  # '运行限额(万千瓦)'---'total_limit'
        output[k]['total_power'] = power.sum() / 10  # '降压功率(万千瓦)'---'total_power'
        output[k]['distri_name'] = list(area_trafo.loc[v.index, 'name_x'].values)  # '分台主变名称'---'distri_name'
        output[k]['distri_power'] = list(power.values / 10)  # '分台降压功率(万千瓦)'--'distri_power'
        output[k]['total_limit_usage'] = output[k]['total_power'] / output[k][
            'total_limit']  # '限额利用率(%)'

    # 获取分区内目标时刻的细节负荷
    load_c = result_dict['load'][area_load_idx, timestep]

    # 去除故障主变;
    if (len(device_name) > 0 and device_name[0] > num_lines):
        n1_trafo_idx = int(device_name[0] - num_lines)
        area_trafo.drop(n1_trafo_idx, inplace=True)
    # 获取分区所有主变的降压功率
    power_ = branch_power[area_trafo.index + num_lines, :].squeeze()
    # 计算受电能力
    trafo_cap = cal_trafo_capability(area_trafo, area_load, power_, load_c, ptdf, num_lines)
    output['power_recev_cap'] = trafo_cap * 0.1  # '受电能力(万千瓦)'---'power_recev_cap'

    return output


def get_trafo_capability_timestep(net, config: dict, result_dict: dict, net_ptdf=None, timestep: int = 0,
                                  area_name: str = None, area_details: dict = dict(), itrafo: bool = True):
    """
    获取某时刻的分区的受电能力和主变信息;
    itrafo: 是否需要主变信息
    """
    output = dict()
    output['power_recev_cap'] = 999999.0
    if area_name is None or area_name == '全省':
        return output

    # 获取分区设备
    area_bus_idx = area_details[area_name]['bus']
    area_bus = net.bus.loc[area_bus_idx, :]
    area_load_idx = area_details[area_name]['load']
    area_load = net.load.loc[area_load_idx, :]
    area_trafo_idx = area_details[area_name]['trafo']
    area_trafo = net.trafo.loc[area_trafo_idx, :]

    # 只考虑500kV或最高电压等级的的主变--'hv_bus' max
    trafo_hv_vn_kv = area_trafo['vn_hv_kv'].max()
    trafo_hv_vn_kv = min(trafo_hv_vn_kv, 500)
    area_trafo = area_trafo.loc[area_trafo['vn_hv_kv'] >= trafo_hv_vn_kv * 0.95, :]

    area_trafo = area_trafo.merge(area_bus, left_on='hv_bus', right_index=True)
    area_trafo = area_trafo.sort_index(axis=0)

    if len(area_trafo) == 0:
        return output
    ptdf = make_PTDF(net, select_idx=config['selected_branch_idx'], config=config,
                     distributed_slack=True) if net_ptdf is None else net_ptdf
    # 计算受电能力
    tf_power = result_dict['trafo_power'][:, timestep].squeeze()
    load_power = result_dict["load"][:, timestep] + result_dict["load_curtailment"][:, timestep]
    trafo_cap = cal_trafo_capability(net, area_name, area_details, tf_power, load_power, ptdf)
    output['power_recev_cap'] = round(float(trafo_cap * 0.1), 1)

    # 根据变电站整合主变
    if itrafo:
        trafo_power_all = result_dict['trafo_power'][:, timestep].squeeze()
        trafo_names_unique = area_trafo['dispname'].unique()
        trafo_names_unique = [name for name in trafo_names_unique if not pd.isna(name)]  # 去除nan值
        trafo_names_unique = [name for name in trafo_names_unique if not ""]  # 去除""值
        trafos_with_name = dict()
        for name in trafo_names_unique:
            trafos_with_name[name] = area_trafo.loc[area_trafo['dispname'] == name, :]

        for k, v in trafos_with_name.items():
            power = trafo_power_all[v.index]
            rate = power / v['stable_limit_mw']
            output[k] = dict()
            output[k]['lat'] = float(v['lat'].values[0])
            output[k]['lon'] = float(v['lon'].values[0])
            output[k]['load_ratio'] = np.abs(rate.values).round(3).tolist()
            output[k]['distri_limit'] = (v['stable_limit_mw'].values * 0.1).round(1).tolist()

            limit = float(v['stable_limit_mw'].sum() * 0.1)

            output[k]['total_limit'] = limit
            output[k]['total_power'] = round(float(power.sum()) * 0.1, 1)
            output[k]['distri_name'] = area_trafo.loc[v.index, 'name_x'].values.tolist()
            output[k]['distri_power'] = (power * 0.1).round(1).tolist()
            output[k]['total_limit_usage'] = round(float(output[k]['total_power'] / output[k]['total_limit']), 1)

    return output  # 万千瓦


def get_area_indices(area_name: str = None, area_details: dict = dict()):
    """
    获取分区内所有设备的索引;
    Args:
        area_name: 分区名称
        area_details:  分区的设备索引dict---里面没有key:'全省'

    Returns:

    """
    # 筛选出全省或分区级各项功率注入节点
    if area_name is not None and area_name != '全省':  # 分区
        area_wind = area_details[area_name]['wind']
        area_solar = area_details[area_name]['solar']
        area_load = area_details[area_name]['load']
        area_gen = area_details[area_name]['gen']
        area_feedin = area_details[area_name]['feedin']
    else:  # 全省--所有
        area_wind = slice(None)
        area_solar = slice(None)
        area_load = slice(None)
        area_gen = slice(None)
        area_feedin = slice(None)

    area_inds = {'wind': area_wind,
                 'solar': area_solar,
                 'load': area_load,
                 'gen': area_gen,
                 'feedin': area_feedin}

    return area_inds


def cal_simu_boundary_delta(net, config: dict, result_dict: dict, user_input: dict, boundary_original: dict,
                            area_name: str = basef.GLOBAL_QW_NAME, area_details: dict = dict()):
    """
    计算推演模拟仿真边界的数据变化：全网比较特殊,有具体的直流feedin信息:'feedin_detail_output'
    # 可以调用:get_simulation_power_boundry获取与输入一致的边界结构;
    考虑：
    1) load\feedin\wind\solar\gas的总体有功调整量;其它暂不支持调节; 其它机组不参与调节,煤机被动平衡，只有gas可调;
    2) 区内嵌入式直流调整量:"dcline"
    3) 区外直流调整细节;
    :return:
    """
    timestep = user_input["timestep"]  # 推演时刻

    power_delta = dict()

    # 以下5个key必须有;--全网可调的设备有功目标值
    boundary_original_area = boundary_original[area_name]
    boundary_original_area = {k: v for k, v in boundary_original_area.items()
                              if k in ['wind_output',
                                       'solar_output',
                                       'load',
                                       'feedin',
                                       'gas_output']}

    for key in boundary_original_area.keys():
        if key not in user_input.keys():
            power_delta[key] = 0
        else:
            power_delta[key] = round(user_input[key] - boundary_original_area[key][timestep], 1)

    # 区内直流dcline变化量;
    power_delta["dcline"] = dict()
    if user_input["dcline"] and "dcline_power_from" in result_dict.keys():
        for dc_k, dc_v in user_input["dcline"].items():
            dc_k_idx = net.dcline[net.dcline['name'] == dc_k].index[0]
            i_ori_val = result_dict["dcline_power_from"][dc_k_idx, timestep] * 0.1
            power_delta["dcline"][dc_k_idx] = round(dc_v - i_ori_val, 0)  # 调节量=新值-原值,--换索引key

    # 直流细节delta,只有全网才有; 全网的'dc_feedin' info-和data_utils.py中统一key:dc_feedin
    power_delta['dc_feedin'] = dict()  # 有可能是2级dict
    if area_name == basef.GLOBAL_QW_NAME and user_input['dc_feedin']:
        feedin_detail_output = boundary_original[area_name]['feedin_detail_output']
        for k, v in feedin_detail_output.items():
            v_input = user_input['dc_feedin'][k]
            if isinstance(v_input, list):
                # 返回的直流单个支路设备的调整细节;就需要逐个设备调整;
                power_delta['dc_feedin'][k] = dict()
                k_rec_indx = config["dc_feedin_dict"][k]
                if len(k_rec_indx) != len(v_input):
                    # 因为不等,直接算总量
                    if isinstance(k_rec_indx, dict):
                        k_rec_indx_list = list(k_rec_indx.values())
                    else:
                        k_rec_indx_list = k_rec_indx  # list
                    v_timestep = (result_dict["feedin_output"][k_rec_indx_list, timestep].sum() * 0.1).round(1)
                    power_delta['dc_feedin'][k] = round(user_input['dc_feedin'][k] - v_timestep, 1)
                else:
                    # 逐个设备调节,key:feedin的索引list;
                    for i_emu, i_key in enumerate(k_rec_indx):
                        i_ori_val = (result_dict["feedin_output"][k_rec_indx[i_key], timestep].sum() * 0.1).round(1)
                        power_delta['dc_feedin'][k][str(k_rec_indx[i_key])] = round(user_input['dc_feedin'][k][
                                                                                        i_emu] - i_ori_val, 1)
            else:
                k_rec_indx = config["dc_feedin_dict"][k]
                if isinstance(k_rec_indx, dict):
                    k_rec_indx_list = list(k_rec_indx.values())
                else:
                    k_rec_indx_list = k_rec_indx  # list
                v_timestep = (result_dict["feedin_output"][k_rec_indx_list, timestep].sum() * 0.1).round(1)
                power_delta['dc_feedin'][k] = round(user_input['dc_feedin'][k] - v_timestep, 1)

    # 机组出力delta--'gen'的总出力变化delta值;
    if 'gen_output' in user_input.keys():
        gen_op_res = result_dict['gen_output'][:, timestep]  # timestep时刻所有机组的初始出力;
        gen_output_init = get_gen_choices(net, gen_op_res, area_name=area_name,
                                          area_details=area_details,
                                          area_or_region_choice=user_input['gen_output']['area_or_region_choice'],
                                          gen_choices=user_input['gen_output']['gen_choices'])
        power_delta['gen_output'] = round(user_input['gen_output']['target'] - gen_output_init['initial'], 1)
    else:  # 如果没有做所有机组层面选择，则使用总燃机出力的调整量
        power_delta['gen_output'] = power_delta['gas_output']
        gen_output_init = {
            'gen_list': {},
            'boundary_range': 0,
            'initial': 0
        }
    return power_delta, gen_output_init


def get_gen_choices(net, gen_output_res, area_name='全省', area_details: dict = dict(),
                    area_or_region_choice='',
                    gen_choices: list = []):
    """
    推演计算,选择可调节的gen列表;
    Args:
        net: case的基础pp.network网络
        gen_output_res: case的teap计算结果dict
        # timestep: 计算的分析时刻序号
        area_name: 分区名称; 'all_zone':全网;
        area_details:  分区具体关联设备索引dict;
        area_or_region_choice: 在全网进行推演时，可选择调节机组的分区或区域;
        gen_choices: 具体选择调节的机组
    Returns:

    """
    # 单台常规机组出力和上下限
    # gen_output_res = res['gen_output'][:,timestep]   #timestep时刻所有机组的初始出力;

    gen_output = dict()  # 出力
    gen_names = dict()  # 机组名称
    gen_max_mw = dict()  # 最大出力值
    all_gas_df = net.gen[net.gen['type'] == 'gas']  # 全网所有的气电机组;
    all_gas_index = np.array(all_gas_df.index.tolist().astype(int))

    for area_or_region in area_details:
        area_gen_idx = area_details[area_or_region]['gen']
        in_arr2 = np.in1d(area_gen_idx, all_gas_index)
        area_gas_idx = area_gen_idx[in_arr2]  # 分区内的gas索引
        gen_output[area_or_region] = gen_output_res[area_gas_idx]  # 分区内的气电原结果出力
        gen_names[area_or_region] = net.gen.loc[area_gas_idx, 'name']  # 分区内的气电名
        gen_max_mw[area_or_region] = net.gen.loc[area_gas_idx, 'max_p_mw']  # 分区内的气电最大发电出力

    # 增加全网气电(gas)机组词典
    gen_output['全省'] = gen_output_res[all_gas_index]
    gen_names['全省'] = all_gas_df['name']
    gen_max_mw['全省'] = all_gas_df['max_p_mw']

    k = 0.1  # MW->万千瓦
    if area_name == '全省':
        # 选定机组后最大最小值
        if area_or_region_choice != '' and area_or_region_choice in area_details:
            gen_names_in_chosen_a = gen_names[area_or_region_choice]
            if gen_choices != []:
                gen_choices_index = gen_names_in_chosen_a[gen_names_in_chosen_a.isin(gen_choices)].index.tolist()
            else:
                gen_choices_index = gen_names_in_chosen_a.index.tolist()
            p_max_sum = gen_max_mw[area_or_region_choice].loc[gen_choices_index].sum() * k  # 机组最大出力限额
            # p_output_sum = gen_output[area_or_region_choice][gen_choices_index].sum() * k
            p_output_sum = gen_output_res[gen_choices_index].sum() * k  # 机组原始出力
            boundary_range = [0, p_max_sum, (p_max_sum - 0) / 10]
        else:
            boundary_range = 0
            p_output_sum = 0
    else:
        if gen_choices != []:
            gen_names_in_chosen_a = gen_names[area_or_region_choice]
            gen_choices_index = gen_names_in_chosen_a[gen_names_in_chosen_a.isin(gen_choices)].index
            p_max_sum = gen_max_mw[area_name].loc[gen_choices_index].sum() * k
            # p_output_sum = gen_output[area_name].loc[gen_choices_index].sum() * k
            p_output_sum = gen_output_res[gen_choices_index].sum() * k
            boundary_range = [0, p_max_sum, (p_max_sum - 0) / 10]
        else:
            boundary_range = 0
            p_output_sum = 0

    output = {
        'gen_list': {k: v.tolist() for k, v in gen_names.items()},
        'boundary_range': boundary_range,
        'initial': p_output_sum
    }

    return output


def cal_delta(net, result_dict, target_time, user_input_init, area_inds):
    '''
    计算各部分增量，stogen默认不支持调节; 此方法中feedin是调节的总量;
    '''

    k = 0.1
    n = 10

    # 各分项原始值
    wind_output = result_dict['wind_output'][area_inds['wind'], target_time].sum() * k
    solar_output = result_dict['solar_output'][area_inds['solar'], target_time].sum() * k
    feedin_output = result_dict['feedin_output'][area_inds['feedin'], target_time].sum() * k
    load = result_dict['load'][area_inds['load'], target_time].sum() * k

    user_input_delta = dict()
    user_input_delta['wind'] = round(user_input_init['wind_output'] - wind_output, 1) * n
    user_input_delta['solar'] = round(user_input_init['solar_output'] - solar_output, 1) * n
    user_input_delta['load'] = round(user_input_init['load'] - load, 1) * n

    # 燃气调节;
    if 'gas_output' in user_input_init.keys():
        gas_idx = net.gen.loc[net.gen['type'] == 'gas'].index
        if not isinstance(area_inds['gen'], slice):
            gas_idx = basef.get_list1_ele_in_list2(gas_idx, area_inds['gen'])
        if len(gas_idx) > 0:
            gas_output = result_dict['gen_output'][gas_idx, target_time].sum() * k
            user_input_delta['gas'] = round(user_input_init['gas_output'] - gas_output, 1) * n

    # 对区外来电需要进行细分--按单设备调节,在外部单独函数进行计算更新
    # user_input_delta["feedin"] = np.zeros(
    #     shape=result_dict['feedin_output'][area_inds['feedin'], target_time].reshape((-1, 1)).shape)
    user_input_delta["feedin"] = round(user_input_init['feedin'] - feedin_output, 1) * n

    return user_input_delta


# def cal_feedin_dev_delta(result_dict, target_time, user_input_init, area_inds, config: dict = dict()):
#     '''
#     计算feedin类型设备的单设备详细增量
#     # 'feedin'中是feedin中所有调节量的和;
#     config:dict,feedin_detail,需要config
#     return:
#     user_input_delta_feedin: {index:delta_val},index:feedin记录支路索引;
#     '''
#
#     k = 0.1
#     n = 10
#
#     # 原始值--area_inds['feedin']
#     feedin_output = result_dict['feedin_output'][:, target_time].reshape((-1, 1)) * k
#
#     user_input_delta_feedin = dict()
#     # 根据config中的dc的配置;分配到具体设备的变化量;均分--# and ('dc_feedin_dict' in config and config['dc_feedin_dict'] != {})
#
#     if 'dc_feedin' in user_input_init.keys() and user_input_init['dc_feedin']:
#         # 遍历user_input_init['dc_feedin']
#         for k, v in config['dc_feedin_dict'].items():
#             # 从result_dict['feedin_output']中获取v索引list,target_time的sum(),然后计算变化值; user_input_init['dc_feedin'][k]
#             i_idx_len = len(v)
#             if k not in user_input_init['dc_feedin'].keys() or i_idx_len == 0:
#                 continue
#             else:
#                 user_k = user_input_init['dc_feedin'][k]
#                 if isinstance(user_k, list) and i_idx_len == len(user_k):
#                     # 逐个调节;  MW
#                     if isinstance(v, dict):
#                         for i_emu, i_dx in enumerate(v):
#                             i_emu_idx_list = v[i_dx]
#                             i_emu_idx_len = len(i_emu_idx_list)
#                             user_input_delta_feedin.update(
#                                 {idx: (user_k[i_emu] - np.sum(feedin_output[i_emu_idx_list, :])) * n / i_emu_idx_len for
#                                  idx in i_emu_idx_list})
#                     else:  # list
#                         for i_emu, i_dx in enumerate(v):
#                             user_input_delta_feedin[i_dx] = round((user_k[i_emu] - feedin_output[i_dx, :].sum()) * n, 0)
#                 else:
#                     # 整体调节; user_k:总目标值;v:关联索引--按比例增加可能会遇到设备为0的情况，无法增加;-按等量相加;
#                     v_idx_list = list(set(chain.from_iterable(v.values()))) if isinstance(v, dict) else v
#                     user_input_delta_feedin.update(
#                         {idx: (user_k - np.sum(feedin_output[v_idx_list, :])) * n / len(v_idx_list) for idx in
#                          v_idx_list})
#
#     # 保留1位小数
#     user_input_delta_feedin = {k: round(v, 0) for k, v in user_input_delta_feedin.items()}
#
#     return user_input_delta_feedin


def cal_feedin_dev_delta(power_delta: dict, config: dict = dict()):
    '''
    计算feedin类型设备的单设备详细增量 MW
    # 'feedin'中是feedin中所有调节量的和;
    config:dict,feedin_detail,需要config
    return:
    user_input_delta_feedin: {index:delta_val},index:feedin记录支路索引;
    '''
    n = 10

    user_input_delta_feedin = dict()
    if "dc_feedin" in power_delta and power_delta["dc_feedin"]:
        for k, v in power_delta["dc_feedin"].items():
            if isinstance(v, dict):
                for sub_key, sub_v in v.items():
                    v_idx_list = eval(sub_key)  # str(list)->list
                    user_input_delta_feedin.update(
                        {idx: sub_v * n / len(v_idx_list) for idx in v_idx_list})
            else:
                # 整体调节,user_k:总目标值;v:关联索引--按比例增加可能会遇到设备为0的情况，无法增加;-按等量相加;
                if k not in config['dc_feedin_dict'].keys():
                    continue
                else:
                    k_idx = config['dc_feedin_dict'][k]  # dict or list
                    v_idx_list = list(set(chain.from_iterable(k_idx.values()))) if isinstance(k_idx, dict) else k_idx
                    user_input_delta_feedin.update(
                        {idx: v * n / len(v_idx_list) for idx in v_idx_list})
    # 保留1位小数
    user_input_delta_feedin = {k: round(v, 0) for k, v in user_input_delta_feedin.items()}

    return user_input_delta_feedin


def cal_pf(net, config,
           result_dict,
           user_input_init, user_input_delta,
           device_name, target_time, area_inds,
           phase_shifter_arg={},
           add_reactance_arg={}):
    """
    直流潮流计算
    简化容抗法做N-1计算
    燃机调整，并输出最后的燃机、区外有功结果值;
    result_dict: 全网的TEAP结果;
    user_input_delta_feedin： 区外单设备调整细节;
    user_input_delta: 非区外设备整体调整细节;
    """

    ### 添加移相器或串抗后的新潮流结果

    ### 如果添加移相器：
    ### bus表中行数、trafo表中行数都发生变化!!!!；
    ### line表中行数未发生变化, 理论上device_name里面的设备序号无需修改
    # 初始的 result_dict['branch_power'][:, target_time].reshape((-1, 1))
    if 'branch_power' in result_dict.keys():
        result_branch_power_trgtm = result_dict['branch_power'][:, target_time]
    else:
        # 如果没有支路潮流,支路潮流 = 'line_power' + 'trafo_power'
        result_branch_power_trgtm = np.concatenate(
            (result_dict['line_power'][:, target_time], result_dict['trafo_power'][:, target_time])).reshape((-1, 1))

    # 考虑机组(影响平衡机选择--将inservice置为False)/线路/trafo的检修,更新拓扑及ptdf;
    n_line_output = len(net.line.index)
    Pbr_max = get_Pbr_max(net, net.line.index, net.trafo.index)
    net, Pbr_max = checkout_ele_maintenance_status(net, Pbr_max, target_time)
    # 考虑新增的设备;
    if (phase_shifter_arg != {}) | (add_reactance_arg != {}):
        # pp net 中添加移相器
        net_copy = copy.deepcopy(net)
        net_copy = prepare_net_with_new_device(net_copy, result_dict, target_time,
                                               phase_shifter_arg=phase_shifter_arg,
                                               add_reactance_arg=add_reactance_arg)
        set_user_pf_options(net_copy, overwrite=True, calculate_voltage_angles=True, ac=False,
                            mode='pf', voltage_depend_loads=False, distributed_slack=True)
        # 完整直流潮流
        rundcpp(net_copy)
        # 覆盖该时刻潮流结果
        branch_power_with_device_mod = np.concatenate(
            (net_copy['res_line']['p_from_mw'].values, net_copy['res_trafo']['p_hv_mw'].values))
        # 如果添加了移相器，支路数会变化。
        # 这里从原结果中提取支路数，然后从新结果中提取需要的支路潮流
        # length_original = result_dict['branch_power'].shape[0]
        length_original = len(net.line) + len(net.trafo)
        # result_dict['branch_power'][:, target_time] = branch_power_with_device_mod[:length_original]
        result_branch_power_trgtm = branch_power_with_device_mod[:length_original]

    # 重新计算ptdf---若添加串抗,ptdf和lodf会发生变化
    if add_reactance_arg != {}:  # 添加串抗;
        net = add_reactance(net, add_reactance_arg)
    # 若有N-1故障设备;-更新net网络拓扑参数
    if len(device_name) > 0:
        # 获取支路故障，根据约定可以获取故障支路的编号，此编号为支路计算ptdf时在branch集中的顺序 branch = [line, trafo]
        # 进行支路N-1的潮流计算需要计算新网架的ptdf，此处简化为将故障线路的电抗值设为999999 故障主变的容量设为0.00001
        for ind, device_fault_num in enumerate(device_name):
            Pbr_max[device_fault_num] = 0.01  # 更新故障设备限额;
            if device_fault_num > n_line_output - 1:  # trafo
                net.trafo.loc[device_fault_num - n_line_output, 'sn_mva'] = 0.01
                net.trafo.loc[device_fault_num - n_line_output, 'stable_limit_mw'] = 0.01
                # net.trafo.loc[device_fault_num - n_line_output, 'vkr_percent'] = 99.9
                # net.trafo.loc[device_fault_num - n_line_output, 'vk_percent'] = 0
            else:  # line
                net.line.loc[device_fault_num, 'x_ohm_per_km'] = 9999999
                net.line.loc[device_fault_num, 'stable_limit_mw'] = 0.01

    # 重新计算ptdf
    ptdf, lodf = make_PTDF_LODF(net, select_idx=config['selected_branch_idx'], config=config,
                                distributed_slack=True)

    # ****对支路设备进行N-1计算*****start******
    if len(device_name) > 0:
        #### 用简化容抗法做N-1
        # 获取支路故障，根据约定可以获取故障支路的编号，此编号为支路计算ptdf时在branch集中的顺序 branch = [line, trafo]
        # 进行支路N-1的潮流计算需要计算新网架的ptdf，此处简化为将故障线路的电抗值设为999999 故障主变的容量设为0.00001
        f_t = np.zeros(shape=(len(device_name), 2))
        for ind, device_fault_num in enumerate(device_name):
            if device_fault_num > n_line_output - 1:
                f_t_i = np.array([net.trafo.loc[device_fault_num - n_line_output, 'hv_bus'],
                                  net.trafo.loc[device_fault_num - n_line_output, 'lv_bus']])
            else:
                f_t_i = np.array([net.line.loc[device_fault_num, 'from_bus'],
                                  net.line.loc[device_fault_num, 'to_bus']])
            f_t[ind, :] = f_t_i  # 涉及到的节点汇总
        f_t = f_t.flatten().astype(int)  # 改为1d
        # 原所有支路上此时刻功率
        # result_dict['branch_power'][:, target_time].reshape((-1, 1))
        branch_power_init = result_branch_power_trgtm.reshape((-1, 1))
        # 原选定支路两端节点上此时刻，正负功率
        branch_power_in = np.array([-branch_power_init[device_name], branch_power_init[device_name]]
                                   ).reshape((-1, 1))
        # 故障引起的所有支路功率变化量
        # 选定支路两端节点的功率通过新的PTDF注入到网架后所有支路功率变化量
        # ptdf_a = ptdf[:, f_t]
        default_branch_power = ptdf[:, f_t] @ branch_power_in
        # debug
        # 将 numpy 数组转换为 pandas DataFrame
        # df = pd.DataFrame(ptdf)
        # df.to_csv('./ptdf_matrix.csv', index=False)

        # 选定支路功率保持原功率
        default_branch_power[device_name] = branch_power_init[device_name]
        # 发生故障后的所有支路的功率
        branch_power_init = branch_power_init - default_branch_power
    else:
        if 'branch_power' in result_dict:
            # branch_power_init = result_dict['branch_power'][:, target_time].reshape((-1, 1))
            branch_power_init = result_branch_power_trgtm.reshape((-1, 1))
        else:
            branch_power_init = np.concatenate(
                (result_dict['line_power'][:, target_time], result_dict['trafo_power'][:, target_time])).reshape(
                (-1, 1))
    # ****对支路设备进行N-1计算*****end********

    ## 计算功率注入变化量
    branch_power_add = np.zeros((ptdf.shape[0], 1))
    gen_power_add = 0  # 常规机组需要的调节量

    # 配合调节不平衡量的机组索引,如果有500kV和苏南/苏北需求(江苏)，再加输入的参数变量;
    # suit_gen_delta_index = net.gen.index.tolist()  # 初始为所有的gen中机组;
    # suit_gen_delta_index = net.gen[net.gen["type"].isin(["coal", "gas"])].index.tolist()  # 初始为所有的gen中煤机+燃气机组;
    slack_gen = get_slack_gen(net)
    suit_gen_delta_index = slack_gen.index.tolist()  # 初始为:所有选择的平衡机(>=500kV,coal机组);

    gen_output_res = result_dict['gen_output'][:, target_time].reshape((-1, 1))  # 初始化;

    ele_result_delta_value = dict()  # 所有设备最终调节量;用于计算最终的功率平衡时各类型设备输出功率
    valid_val_delta = 1.0  # 有效的调节量;
    for k, v in user_input_delta.items():  # v:MW
        # 新能源\区外来电
        if k in ["wind", "solar", "feedin"] and abs(v) > valid_val_delta:
            gen_power_add -= v
            k_type_output = result_dict[f'{k}_output'][area_inds[k], target_time].reshape((-1, 1))  # 初始出力
            k_type_cap = net[k].max_p_mw.iloc[area_inds[k]].values.reshape((-1, 1))  # 装机
            k_type_ptdf = ptdf[:, net[k].bus.iloc[area_inds[k]].values]
            branch_power_add += cal_branch_power_add(v, k_type_output, k_type_cap, k_type_ptdf)
            ele_result_delta_value[k] = v  # 推演结束后设备的增加变化量;
        # 直流调整细节;
        elif k == 'dc_feedin':
            feedin_v = np.array(list(v.values()))
            gen_power_add -= feedin_v.sum()  # 总变化量
            feedin_bus = net.feedin.loc[list(v.keys()), "bus"].values
            branch_power_add += ptdf[:, feedin_bus] @ (feedin_v.reshape((-1, 1)))
            ele_result_delta_value[k] = feedin_v.sum()
        # 负荷
        elif k == 'load' and abs(v) > valid_val_delta:
            gen_power_add += v
            load = result_dict['load'][area_inds['load'], target_time].reshape((-1, 1))
            load_cap = net.load.max_p_mw.iloc[area_inds['load']].values.reshape((-1, 1))
            load_ptdf = ptdf[:, net.load.bus.iloc[area_inds['load']].values]
            branch_power_add -= cal_branch_power_add(v, load, load_cap, load_ptdf)
            ele_result_delta_value[k] = v
        # 燃气机组
        elif k == 'gas' and abs(v) > valid_val_delta:  # gas
            gen_power_add -= v
            gas_idx = net.gen.loc[net.gen['type'] == 'gas'].index
            if not isinstance(area_inds['gen'], slice):
                gas_idx = basef.get_list1_ele_in_list2(gas_idx, area_inds['gen'])  # 分区/全网的gas机组索引
            gas_out = result_dict['gen_output'][gas_idx, target_time].reshape((-1, 1))
            gas_cap = net.gen.max_p_mw.iloc[gas_idx].values.reshape((-1, 1))
            gas_ptdf = ptdf[:, net.gen.bus.iloc[gas_idx].values]
            branch_power_add += cal_branch_power_add(v, gas_out, gas_cap, gas_ptdf)
            # 平衡机里去除可能存在的gas机组,平衡机组只有非燃气机组了
            suit_gen_delta_index = (slack_gen[~slack_gen["type"].isin(["gas"])]).index.tolist()
            # 将已调过的燃气机组的初值置为0
            gen_output_res[gas_idx, :] = 0
            ele_result_delta_value[k] = v
        # 区内直流调节,不影响全网功率平衡和六维指标,只影响潮流分布;
        elif k == 'dcline' and user_input_delta["dcline"]:
            # user_input_delta["dcline"] 是索引: 变化量; 输入调整的是送端; 送端节点增加负荷量：(-1)* 调整量,同时受端节点增加负荷：调整量 * (1-loss_rate),单节点调整;
            dcline_keys = list(user_input_delta["dcline"].keys())
            dcline_values = np.array(list(user_input_delta["dcline"].values()))
            frombus_ptdf = ptdf[:, net.dcline.from_bus.iloc[dcline_keys]]
            tobus_ptdf = ptdf[:, net.dcline.to_bus.iloc[dcline_keys]]

            dcline_df = net.dcline
            dcline_df["from_add"] = 0  # 送端调整量
            dcline_df.loc[dcline_keys, "from_add"] = dcline_values * (-1)
            dcline_df["to_add"] = 0  # 受端调整量
            dcline_df.loc[dcline_keys, "to_add"] = dcline_df['from_add'] * (1 - dcline_df['loss_rate'])
            from_add = frombus_ptdf @ dcline_df["from_add"].values.reshape((-1, 1))
            to_add = tobus_ptdf @ dcline_df["to_add"].values.reshape((-1, 1))
            branch_power_add += (from_add + to_add)
        else:
            pass

    # print(branch_power_add)
    # 燃机的情况，需要满足的情况包括：给出的燃机，固定为给出的计划；没有给出的燃机，一起分配
    # 貌似这里分区也应该用全省值，因为用户传进来的燃机编号需要从所有燃机中indexing
    if "gas" not in ele_result_delta_value.keys():
        ele_result_delta_value["gas"] = 0
    if 'fixed_gen' in user_input_init.keys():
        n_fixed_gen = len(user_input_init['fixed_gen'])
        if n_fixed_gen > 0:
            for k, v in user_input_init['fixed_gen'].items():
                gas_add = v * 10 - gen_output_res[k, 0]  # 0列,shape:(-1,1),MW
                gen_power_add -= gas_add
                gen_output_res[k, 0] = gas_add
                # 将固定计划的机组从配合调节的机组索引中去除
                if k in suit_gen_delta_index:
                    suit_gen_delta_index.remove(k)
                # "gas"机组总量调整;
                ele_result_delta_value["gas"] += gas_add
        else:
            pass
    else:
        pass

    # 分摊剩余的不平衡量--剩余所有平衡机组上
    if len(suit_gen_delta_index) == 0:
        suit_gen_delta_index = net.gen.index.tolist()  # 去除固定计划机组和燃气机组，没有可调节的机组,只能机组整体一起调节;
    if abs(gen_power_add) > 1:
        if user_input_init['case_scenerio'] == 'jiyuan':
            # 济源由于是配网-没有自有机组-不平衡量由feedin的下网功率来分摊;
            ori_feedin_suit_output_rate = gen_power_add / result_dict['feedin_output'][:, target_time].sum()
            feedin_output_res = result_dict['feedin_output'][:, target_time].reshape((-1, 1))  # 初始化;
            feedin_output_res_n = feedin_output_res * ori_feedin_suit_output_rate
            branch_power_add += ptdf[:, net.feedin.bus.values] @ feedin_output_res_n
            # 增加的是feedin的功率
            if "feedin" in ele_result_delta_value.keys():
                ele_result_delta_value["feedin"] += gen_power_add
            else:
                ele_result_delta_value["feedin"] = gen_power_add
        else:
            ori_gen_suit_output_rate = gen_power_add / result_dict['gen_output'][
                suit_gen_delta_index, target_time].sum()
            gen_delta_val = np.zeros(gen_output_res.shape)  # 机组变化量;
            gen_delta_val[suit_gen_delta_index] = gen_output_res[suit_gen_delta_index] * ori_gen_suit_output_rate
            branch_power_add += ptdf[:, net.gen.bus.values] @ gen_delta_val
            # 增加的是suit_gen_delta_index的机组;
            gen_adj_df = net["gen"][net["gen"].index.isin(suit_gen_delta_index)]
            for type, grp_i in gen_adj_df.groupby('type'):
                if type == "gas":
                    ele_result_delta_value[type] += gen_delta_val[grp_i.index, 0].sum()
                else:
                    ele_result_delta_value[type] = gen_delta_val[grp_i.index, 0].sum()
    branch_power = branch_power_add + branch_power_init
    branch_power[np.abs(branch_power) < 1e-3] = 0

    # 最后对有N-1断开的设备的支路有功强制设置为0
    branch_power[device_name] = 0

    # 根据(ele_result_delta_value)整理最终所有类型的功率平衡出力;

    return branch_power, Pbr_max, ptdf, lodf


def vip_lines_N_1_1(net, branch_power, lodf, Pbr_max):
    '''
    用LODF筛选出重点保障通道
    注意输入的lodf有可能是简化容抗以后计算出来的
    '''

    # 筛选出500kV设备
    net.line["line_index"] = net.line.index
    # bus_kv = net.bus.loc[:, ['vn_kv']]
    # bus_kv["bus_index"] = bus_kv.index
    # line_kv = pd.merge(net.line, bus_kv, how='left', left_on='from_bus', right_on='bus_index')
    line_kv = net.line
    line_500_kv_index = filter_branch_idx(line_kv, 500, 600, 'line')
    net.trafo["trafo_index"] = net.trafo.index
    # trafo_500_kv_index = filter_branch_idx(net.trafo, 500, 600, 'trafo')

    #### 用LODF遍历所有500kV设备，做N-1-1
    # 用LODF计算第二条支路故障后其他支路功率变化量，并加到原功率上
    # 各自保存第二次故障后所有支路新功率
    branch_power_array = np.zeros(shape=(branch_power.shape[0], len(line_500_kv_index)))
    for ind, device_fault_num_2 in enumerate(line_500_kv_index.values):
        branch_power_array[:, ind] = branch_power.squeeze() + lodf[:, device_fault_num_2] * branch_power[
            device_fault_num_2]

    #### N-1-1后引发限额利用率大于80%设备最多的若干条故障支路
    n_lines_needed = 20
    rate = np.absolute(branch_power_array[line_500_kv_index]) / Pbr_max[line_500_kv_index]
    rate_heavy = (rate >= .8).sum(axis=0)
    line_vip_ind = line_500_kv_index[rate_heavy.argsort()[-100:][::-1]]
    lines_vip_name = net.line.loc[line_vip_ind, 'name'].tolist()
    lines_vip_heavy_count = rate_heavy[rate_heavy.argsort()[-100:][::-1]]
    lines_vip_init = {n: c for n, c in zip(lines_vip_name, lines_vip_heavy_count) if 'Z' not in n}
    top_line_names = list(lines_vip_init.keys())[:n_lines_needed]
    lines_vip = {n: c for n, c in lines_vip_init.items() if n in top_line_names}

    return lines_vip


def ana_power_supply_strategy():
    """
    分析返回保供对应措施策略; 源测、网侧、荷侧
    :return:
    """
    dict_gen_output = dict()
    load_transfer = dict()
    demand_man = dict()
    # if (area_name != '全省') and (psm_ < 0):
    #     psm_init = {a: liuwei['power_supply_margin'] for a, liuwei in chushizhi_timestep.items()}
    #     psm_init[area_name] = psm_
    #     dict_gen_output, load_transfer, demand_man = ss_decisions(net, result_dict, area_details,
    #                                                               area_names_list=[area_name],
    #                                                               area_psm_value=psm_init,
    #                                                               timestep=timestep,
    #                                                               upload_dir='./')
    return dict_gen_output, load_transfer, demand_man


def get_graph_dict(net, config, result_dict, branch_power, Pbr_max, device_name, target_time,
                   area_name=None, area_details: dict = dict()):
    '''
    地图上展示潮流
    故障计算后变化幅度大的设备标注
    area_deteails:分区关联设备索引;
    '''
    # selected_vn_kv = int(config['topo_dis_vn_kv'])
    if area_name is None or area_name == '' or area_name == '全省':
        selected_vn_kv = int(config['topo_dis_vn_kv'])
    else:
        selected_vn_kv = min(220, int(config['topo_dis_vn_kv']))
    graph_bus_unique, graph_line_unique, graph_dcline_unique = select_available_device(net,
                                                                                       disp_type=['DC', 'dc', 'switch',
                                                                                                  'station', "coal", "gas",
                                                                                                  "new", "nuclear", "pump",
                                                                                                  "solar", "stogen", "wind"],
                                                                                       area_details=area_details,
                                                                                       area_name=area_name,
                                                                                       grp_vn_kv=selected_vn_kv)

    n_line_output = len(net.line.index)
    # n_trafo_output = len(trafo_idx)

    line_power = branch_power[0:n_line_output, :]
    trafo_power = branch_power[n_line_output:, :]
    if "dcline_power_from" in result_dict:
        dcline_power = result_dict["dcline_power_from"][:, target_time].reshape((-1, 1))
    else:
        dcline_power = None

    if "dcline" in net.keys() and not net.dcline.empty:
        dcline_df = net.dcline
    else:
        dcline_df = None

    graph_dict = get_graph_data(graph_bus_unique, graph_line_unique, net.line, net.trafo, line_power, trafo_power,
                                full_dcline=dcline_df, graph_dcline_unique=graph_dcline_unique,
                                dcline_powerflow=dcline_power)
    graph_dict["N-1flag"] = {"line": [], "trafo": []}
    # print('=============dispname===================')
    # print(net.bus.loc[:, 'dispname'])
    if len(device_name) > 0:
        for ind, device_fault_num in enumerate(device_name):
            if device_fault_num > n_line_output - 1:
                f_t = [net.trafo.loc[device_fault_num - n_line_output, 'hv_bus'],
                       net.trafo.loc[device_fault_num - n_line_output, 'lv_bus']]
                graph_dict["N-1flag"]["trafo"].append({
                    "name": net.bus.loc[f_t[0], 'dispname'],
                })
            else:
                f_t = [net.line.loc[device_fault_num, 'from_bus'],
                       net.line.loc[device_fault_num, 'to_bus']]

                graph_dict["N-1flag"]["line"].append({
                    "fromName": net.bus.loc[f_t[0], 'dispname'],
                    "toName": net.bus.loc[f_t[1], 'dispname'],
                })
                graph_dict["N-1flag"]["line"].append({
                    "fromName": net.bus.loc[f_t[1], 'dispname'],
                    "toName": net.bus.loc[f_t[0], 'dispname'],
                })

    # print(graph_dict["N-1flag"])
    bus_dict = graph_dict['bus']
    line_dict = graph_dict['line']
    dcline_dict = graph_dict['dcline']
    for i in range(len(bus_dict)):
        if math.isnan(bus_dict[i]['powerflow']) or math.isinf(bus_dict[i]['powerflow']):
            graph_dict['bus'][i]['powerflow'] = 0
    for i in range(len(line_dict)):
        if math.isnan(line_dict[i]['powerflow']) or math.isinf(line_dict[i]['powerflow']):
            graph_dict['line'][i]['powerflow'] = 0
    for i in range(len(dcline_dict)):
        if math.isnan(dcline_dict[i]['powerflow']) or math.isinf(dcline_dict[i]['powerflow']):
            graph_dict['dcline_dict'][i]['powerflow'] = 0
    ##### 结束 地图数据准备 #####

    rate = np.absolute(branch_power) / Pbr_max
    _ret_dict = dict()

    ##### 指标计算 故障计算后变化幅度大的设备
    if len(device_name) > 0:
        # N-1故障后限额利用率变化幅度较大的设备列表和变化量
        branch_power_ori = result_dict['branch_power'][:, target_time].reshape((-1, 1))
        rate_ori = np.absolute(branch_power_ori) / Pbr_max
        line_trafo_names = net.line.loc[:, 'name'].tolist() + net.trafo.loc[:, 'name'].tolist()
        rate_diff = pd.DataFrame(data=np.concatenate([rate_ori, rate, (rate - rate_ori)], axis=1),
                                 columns=['original', 'defaulted', 'diff'])
        rate_diff['instrument_name'] = line_trafo_names

        line_rate_diff_top = rate_diff.iloc[:n_line_output, :].sort_values(by=['diff'], ascending=False).head(20)
        trafo_rate_diff_top = rate_diff.iloc[n_line_output:, :].sort_values(by=['diff'], ascending=False).head(20)

        _ret_dict['line_large_rate_diff'] = df_to_json(line_rate_diff_top, with_index=False)
        _ret_dict['trafo_large_rate_diff'] = df_to_json(trafo_rate_diff_top, with_index=False)

        # 地图上展示
        graph_dict["large_rate_diff"] = {"line": [], "trafo": []}
        f_t = [net.trafo.loc[trafo_rate_diff_top.index - n_line_output, 'hv_bus'].values.tolist(),
               net.trafo.loc[trafo_rate_diff_top.index - n_line_output, 'lv_bus'].values.tolist()]
        for ind in range(len(f_t[0])):
            graph_dict["large_rate_diff"]["trafo"].append({
                "name": net.bus.loc[f_t[0][ind], 'dispname'],
            })

        f_t = [net.line.loc[line_rate_diff_top.index, 'from_bus'].values.tolist(),
               net.line.loc[line_rate_diff_top.index, 'to_bus'].values.tolist()]
        for ind in range(len(f_t[0])):
            graph_dict["large_rate_diff"]["line"].append({
                "fromName": net.bus.loc[f_t[0][ind], 'dispname'],
                "toName": net.bus.loc[f_t[1][ind], 'dispname'],
            })
            graph_dict["large_rate_diff"]["line"].append({
                "fromName": net.bus.loc[f_t[1][ind], 'dispname'],
                "toName": net.bus.loc[f_t[0][ind], 'dispname'],
            })
        # print(list(line_rate_diff_top.head().index))
        # print(f_t)
        # print(net.bus.loc[f_t[1], 'dispname'])
        # print(graph_dict["N-1flag"])
        # print(graph_dict["large_rate_diff"])

    return graph_dict, _ret_dict


def checkout_ele_maintenance_status(net, Pbr_max, target_time):
    """
    # 检查net.timeseries中有没有matenance的机组/line/trafo,如果有修正net网络的设备参数;
    net: 拓扑表 = net
    Pbr_max： 限额列表
    target_time： 仿真推演时刻
    :return:
    net
    """
    timeseries_type_dict = net.timeseries["type"].to_dict()  # 将net.timeseries的 index:type 生成dict
    timeseries_arrs = net.timeseries.loc[:, f"t{int(target_time) + 1}_pu"].values  # shape:(n,)

    # 获取处理停运设备;
    def deal_col_timeseries(date_df, bcol_timeseries_check: bool = True, key_str: str = "maintenance"):
        if bcol_timeseries_check:
            date_df['timeseries'] = date_df['timeseries'].fillna("")
            date_df['timeseries'] = date_df['timeseries'].values.astype('str')
            # 如果'timeseries'包含'[',']'用''替换
            date_df['timeseries'] = date_df['timeseries'].str.replace('[', '', regex=False)
            date_df['timeseries'] = date_df['timeseries'].str.replace(']', '', regex=False)
            date_df['timeseries'] = date_df['timeseries'].str.replace('，', ',', regex=False)
        # 逐行处理
        date_df[key_str] = date_df.apply(
            lambda row: basef.process_row_str(row, timeseries_type_dict, "timeseries", key_str), axis=1)
        return date_df

    def deal_off_ele_params(ele_ori_df: pd.DataFrame, ele_type: str):
        ele_ori_df = deal_col_timeseries(ele_ori_df)
        ele_df = ele_ori_df[ele_ori_df['maintenance'] != -1]
        # ele_df 添加列 'maintenance_flag' 等于 timeseries_arrs[row['maintenance']]的值
        ele_df['maintenance_flag'] = ele_df['maintenance'].apply(lambda x: timeseries_arrs[x]).values.astype(int)
        ele_df = ele_df[ele_df['maintenance_flag'] == 1]
        if ele_type == 'trafo':
            nline = len(net.line)
            net.trafo.loc[ele_df.index, 'sn_mva'] = 0.01
            net.trafo.loc[ele_df.index, 'stable_limit_mw'] = 0.01
            # net.trafo.loc[ele_df.index, 'vkr_percent'] = 99.9
            # net.trafo.loc[ele_df.index, 'vk_percent'] = 0
        elif ele_type == 'line':
            nline = 0
            net.line.loc[ele_df.index, 'r_ohm_per_km'] = 9999999
            net.line.loc[ele_df.index, 'x_ohm_per_km'] = 9999999
            net.line.loc[ele_df.index, 'stable_limit_mw'] = 0.01
        else:
            raise Exception("unknown ele type")

        # 更新设备限额;
        device_fault_no = nline + np.array(ele_df.index.tolist())
        Pbr_max[list(device_fault_no)] = 0.01  # 更新故障设备限额;Pbr_max: np.array
        return

    # 处理gen--强关和检修的机组都不能选做平衡机;
    gen_df = deal_col_timeseries(net.gen, True, 'maintenance')
    gen_df = gen_df[gen_df['maintenance'] != -1]
    gen_df['maintenance_flag'] = gen_df['maintenance'].apply(lambda x: timeseries_arrs[x]).values.astype(int)
    gen_maint_indx = gen_df[gen_df['maintenance'] == 1].index.tolist()

    # gen_on_off = 0 强关
    gen_onoff_df = deal_col_timeseries(net.gen, False, 'gen_on_off')
    gen_onoff_df = gen_onoff_df[gen_onoff_df['gen_on_off'] != -1]
    gen_onoff_df['gen_on_off_flag'] = gen_onoff_df['gen_on_off'].apply(lambda x: timeseries_arrs[x]).values.astype(int)
    gen_off_indx = gen_onoff_df[gen_onoff_df['gen_on_off_flag'] == 0].index.tolist()

    gen_outservice_idx = set(gen_maint_indx + gen_off_indx)
    net.gen.loc[list(gen_outservice_idx), "in_service"] = False
    # gen处理结束;

    if "timeseries" in net.line.columns:
        deal_off_ele_params(net.line, 'line')
    if "timeseries" in net.trafo.columns:
        deal_off_ele_params(net.trafo, 'trafo')
    return net, Pbr_max
