import numpy as np
import pandas as pd
from alg.cal_ssmetrics.base_func import (get_time_range, spring_month, summer_month, autumn_month, winter_month)


def check_ratio_vn_kv(cal_result_ratio):
    """
    过滤设备电压等级
    :return:
    """
    # 筛选目标需要的电压等级以上的设备
    ratio_vn_kv_valid_dict = dict()
    valid_vn_kv = 500.0 * 0.9  # 过滤电压等级;

    # 过滤设备;
    for key, val in cal_result_ratio.items():
        if "vn_kv" in val:
            # 有效设备位置
            positions = np.where(np.array(val["vn_kv"]) >= valid_vn_kv)[0]  # vn_kv>=500的值的位置
            if len(positions) == 0:
                positions = np.where(np.array(val["vn_kv"]) >= 110 * 0.9)[0]

            ratio_vn_kv_valid_dict[key] = dict()
            for key2, val2 in cal_result_ratio[key].items():
                ratio_vn_kv_valid_dict[key][key2] = np.array(val2)[positions].tolist()
        else:
            ratio_vn_kv_valid_dict[key] = val
    return ratio_vn_kv_valid_dict


def data_df_freq_distributed_base(_data_df, freq_h: str = "month", agg_dict: dict = dict()):
    """
    根据freq_h的类型将_data_df进行聚合并进行分布特性数据统计
    :param _data_df:
    :param freq_h:
    :param agg_dict: 列groupby聚合的方式"max","min","mean","sum",等
    :return:
    """
    if freq_h not in ["month", "season", "hour"]:
        raise TypeError(f"输入的统计分析类型不支持：{freq_h}")

    if freq_h == "season":
        season_dict = {'1': spring_month, '2': summer_month, '3': autumn_month, '4': winter_month}
        # 创建一个反向字典，用于查找 value 对应的 key
        reverse_season_dict = {v: k for k, vals in season_dict.items() for v in vals}
        _data_df["month"] = _data_df.index.month  # 赋值月度
        _data_df["season"] = _data_df['month'].map(reverse_season_dict).astype(int)  # 季度
        groupby_col = "season"
        index_list = [i for i in range(1, 5)]
    elif freq_h == "month":
        _data_df["month"] = _data_df.index.month  # 赋值月度
        groupby_col = "month"
        index_list = [i for i in range(1, 13)]
    else:
        _data_df["hour"] = _data_df.index.hour  # 赋值时刻
        groupby_col = "hour"
        index_list = [i for i in range(24)]

    # # 聚合方法列表，长度为 DataFrame 的列数
    if agg_dict:
        agg_dict_use = agg_dict
    else:
        # 创建一个字典，将每列与对应的聚合方法关联
        agg_dict_use = {col: "sum" for col in _data_df.columns.tolist() if col not in ["month", groupby_col]}
    # 对 DataFrame 进行重采样，并应用不同的聚合方法
    grouped_df = _data_df.groupby(groupby_col).agg(agg_dict_use)
    # 补全索引并填充缺失行数据为 0
    grouped_df = grouped_df.reindex(index_list, fill_value=0)

    return grouped_df


def interface_select_trafo(inf_dict: dict, net=None, inf_ele_type="trafo", area_details_dict=None):
    # interface筛选
    if net is not None and inf_ele_type in ["trafo", "line"]:
        inf_of_ele_idx = set(net[inf_ele_type]["interface"].values.tolist())
        inf_of_ele_idx = [int(i) for i in inf_of_ele_idx if not pd.isna(i)]
        inf_ele_df = pd.DataFrame(inf_dict)
        inf_ele_df = inf_ele_df[inf_ele_df["index"].isin(inf_of_ele_idx)]
        for i_col in inf_ele_df.columns.tolist():
            inf_dict[i_col] = inf_ele_df[i_col].values.tolist()
        if inf_ele_type == "trafo" and area_details_dict:
            # 根据主变断面关联的主变所属分区增加分区名称信息
            inf_zone = [""] * len(inf_ele_df)
            ele_i = -1
            for _, row in inf_ele_df.iterrows():
                ele_i += 1
                inf_trafo = net[inf_ele_type][net[inf_ele_type]["interface"] == row["index"]]
                for key, val in area_details_dict.items():
                    if inf_trafo.index.tolist()[0] in val["trafo"]:
                        inf_zone[ele_i] = key
                        break
            inf_dict["relay_zone"] = inf_zone
    return inf_dict


def series_coldelta_sorted(names, arr1, sort_by: str = "diff"):
    """
    将
    :param names: 矩阵行关联名称;
    :param arr1: 矩阵
    :param sort_by: 排序依据;"diff"列差值和; "sum": 列和
    :return:
    """
    if len(names) != arr1.shape[0]:
        raise ValueError("names and arr1 must have the same row length")

    if sort_by:
        delta = np.diff(arr1, axis=1)  # 计算每行相邻列之间的差值
        delta_sum = np.sum(delta, axis=1)  # 计算每行差值的和
    else:
        delta_sum = np.sum(arr1, axis=1)  # 计算每行值的和

    # 将ele_name 和rlt_arrs组成df
    ele_rlt_df = pd.DataFrame({
        "name": names,
        "delta_": delta_sum,
    })

    # 将 结果矩阵转换为 DataFrame，并指定列名
    df_matrix = pd.DataFrame(arr1, columns=[f'col{i + 1}' for i in range(arr1.shape[1])])

    ele_rlt_df = pd.concat([ele_rlt_df, df_matrix], axis=1)
    ele_rlt_df.sort_values(by=["delta_"], ascending=False, inplace=True)

    # 转为dict输出,key:name, val= col
    selected_columns = ele_rlt_df.columns[2:]  # 从第3列到最后一列
    result_dict = ele_rlt_df.set_index('name')[selected_columns].apply(lambda row: row.tolist(), axis=1).to_dict()
    return result_dict


def exchange_data_to_qrformat(ori_data: np.ndarray, config: dict = dict(), mk_val: list = [0, 320],
                              flag_val: list = [2, 1, 0], sta_type: str = "min"):
    """
    将数据转为qr二维码数据格式; 月-日,日-时
    ori_data: 原始数据dict, key:series
    config: 配置参数字典.提供start_time,sum_snap
    mk_val: 着色门槛值; [0,320]
    flag_val: 例如:0--供电裕度充足;1--供电裕度紧张;2--供电裕度不足(缺电)  [2,1,0]
    """
    # 获取时间范围
    _tm_df = get_time_range(config['start_time'], config['sum_snap'])
    # 计算年/月/日/时分布
    _tm_df['month'], _tm_df['day'], _tm_df['hour'] = _tm_df.index.month, _tm_df.index.day, _tm_df.index.hour

    # 生成QR图
    rtn_data = exchange_data_to_qrformat_v2(_tm_df=_tm_df, ori_data=ori_data, mk_val=mk_val, flag_val=flag_val,
                                            sta_type=sta_type)
    return rtn_data


def exchange_data_to_qrformat_v2(_tm_df: pd.DataFrame, ori_data: np.ndarray, mk_val: list = [0, 320],
                                 flag_val: list = [2, 1, 0], sta_type: str = "min"):
    """
    将数据转为qr二维码数据格式; 月-日,日-时
    ori_data: 原始数据dict, key:series
    config: 配置参数字典.提供start_time,sum_snap
    mk_val: 着色门槛值; [0,320]
    flag_val: 例如:0--供电裕度充足;1--供电裕度紧张;2--供电裕度不足(缺电)  [2,1,0]
    sta_type: 日聚合方式: "min":取最小值; "max":最大值; "mean":均值
    """
    # # 获取时间范围
    # _tm_df = get_time_range(config['start_time'], config['sum_snap'])
    # # 计算年/月/日/时分布
    # _tm_df['month'] = _tm_df.index.month
    # _tm_df['day'] = _tm_df.index.day
    # _tm_df['hour'] = _tm_df.index.hour
    _tm_df['dataVal'] = ori_data.round(4)  # 时序数据
    # _tm_ori_df = _tm_df.copy()
    # tm_sum = len(_tm_df)
    # if tm_sum != 8760 and tm_sum != 8784:
    #     # 需要补全时序;生成完整的时间索引
    #     strat_year = _tm_df.index.year[0]
    #     full_date_rng = pd.period_range(start=pd.to_datetime(f'{strat_year}-01-01 00:00'),
    #                                     end=pd.to_datetime(f'{strat_year}-12-31 23:00'), freq='H')
    #     # 重新索引 DataFrame，补全缺失的日期和小时行记录，列缺失值填充为 -1
    #     # _tm_full_df = _tm_df.reindex(full_date_rng, fill_value=-1)
    #     _tm_df = pd.DataFrame(index=full_date_rng, columns=_tm_df.columns).fillna(0)  # 裕度值;
    #     _tm_df.update(_tm_ori_df)

    # 计算年/月/日/时分布
    _tm_df['month'], _tm_df['day'], _tm_df['hour'] = _tm_df.index.month, _tm_df.index.day, _tm_df.index.hour
    _tm_df["data"] = [i for i in range(len(_tm_df))]

    # 转为key:value格式;
    balanec_mar_mon_dict = dict()

    # 按月、日聚合(用resample不用group)生成新的_tm_monthday_df，相同月/peaking_gap\power_supply_slack的最大值
    _tm_months = _tm_df['month'].unique().tolist()  # 月份;
    if sta_type == "min":
        _tm_daily_max_df = _tm_df.resample('D').min()  # 裕度最小值;
    elif sta_type == "max":
        _tm_daily_max_df = _tm_df.resample('D').max()  # 裕度最大值;
    else:
        _tm_daily_max_df = _tm_df.resample('D').mean()  # 裕度均值;
    # 时序数据着色标志
    bins = [-np.inf] + mk_val + [np.inf]  # 增加区间边界
    indices = np.digitize(_tm_daily_max_df['dataVal'].values, bins) - 1  # 使用 np.histogram 将arr映射到对应的区间
    _tm_daily_max_df['dataFlag'] = np.array(flag_val)[indices]

    # 按月group;
    _tm_grp_mon_df = _tm_daily_max_df.groupby(['month'])
    for i_m in _tm_grp_mon_df.groups.keys():
        # 对grp_i进行按'day'统计
        grp_i = _tm_grp_mon_df.get_group(i_m)
        n_days = len(grp_i['day'])  # days
        dataFlag = grp_i['dataFlag'].values.astype(int).tolist()
        dataVal = grp_i['dataVal'].values.astype(float).tolist()
        balanec_mar_mon_dict[i_m] = [{"dataFlag": -1, "dataVal": -1}] * 31  # 月31天数据,list()
        # balanec_mar_mon_dict[i_m] = grp_i.to_dict(orient='records')
        for i_day in range(1, n_days + 1):
            balanec_mar_mon_dict[i_m][i_day - 1] = {
                "dataFlag": dataFlag[i_day - 1],
                "dataVal": dataVal[i_day - 1]
            }

    # 生成每月的日/小时数据矩阵
    if len(ori_data) != len(_tm_df):
        indices = np.digitize(_tm_df['dataVal'].values, bins) - 1
    else:
        indices = np.digitize(ori_data, bins) - 1  # 使用 np.histogram 将arr映射到对应的区间
    _tm_df['dataFlag'] = np.array(flag_val)[indices]

    balanec_mar_monday_dict = dict()
    _tm_grp_mon_day_df = _tm_df.groupby(['month'])
    # 遍历_tm_grp_mon_df
    for i_m in _tm_grp_mon_day_df.groups.keys():
        # 对grp_i进行按'day'分组
        grp_month = _tm_grp_mon_day_df.get_group(i_m)
        _tm_grp_day_df = grp_month.groupby(['day'])
        daykeys = list(_tm_grp_day_df.groups.keys())
        n_days = len(daykeys)  # num of days
        # 开始填充数据;
        balanec_mar_monday_dict[i_m] = list()
        for i_d in _tm_grp_day_df.groups.keys():
            # 对grp_i进行按'day'统计
            grp_day_i = _tm_grp_day_df.get_group(i_d)
            mar_monday_list = [{"dataFlag": -1, "dataVal": -1, "timeIndex": -1}] * 24  # 24小时
            dataFlag = grp_day_i['dataFlag'].values.astype(int).tolist()
            dataVal = grp_day_i['dataVal'].values.astype(float).tolist()
            timeIndex = grp_day_i['data'].values.astype(int).tolist()  # 时序索引列;
            for i_h in range(0, 24):
                mar_monday_list[i_h] = {
                    "dataFlag": dataFlag[i_h],
                    "dataVal": dataVal[i_h],
                    "timeIndex": timeIndex[i_h],
                }
            balanec_mar_monday_dict[i_m].append(mar_monday_list)
        # 不足31天的全部补-1
        if n_days < 31:
            for _ in range(31 - n_days):
                mar_monday_list_ext = [{"dataFlag": -1, "dataVal": -1, "timeIndex": -1}] * 24
                balanec_mar_monday_dict[i_m].append(mar_monday_list_ext)
    # 返回数据
    rtn_data = {
        "month_day": balanec_mar_mon_dict,
        "day_hour": balanec_mar_monday_dict
    }
    return rtn_data
