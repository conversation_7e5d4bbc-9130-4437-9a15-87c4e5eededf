import requests


def test_zhuangji_capacity(base_url, case_id):
    """
    获取装机容量
    """
    url = f"{base_url}/zhuangji/capacity"
    params = {"case_id": case_id}
    resp = requests.get(url=url, params=params).json()
    print(resp)
    assert resp.get("code") == 200
    assert len(resp.get("data", [])) > 0


def test_device_number(base_url, case_id):
    """
    获取设备数目
    """
    url = f"{base_url}/device/number"
    params = {"case_id": case_id}
    resp = requests.get(url=url, params=params).json()
    print(resp)
    assert resp.get("code") == 200
    assert resp.get("code") == 200


def test_annual_extreme_data(base_url, case_id):
    """
    全年极值信息，示例：最大负荷, 最大峰谷差, 最大供电缺口
    """
    url = f"{base_url}/annual/extreme/data"
    params = {"case_id": case_id}
    resp = requests.get(url=url, params=params).json()
    print(resp)
    assert resp.get("code") == 200


def test_map_network_data(base_url, case_id):
    """
    电网拓扑地图及分区
    """
    url = f"{base_url}/map/network/data"
    params = {"case_id": case_id, "time_no": 1}
    resp = requests.post(url=url, json=params).json()
    print(resp)
    assert resp.get("code") == 200


def test_zone_all_psm(base_url, case_id):
    """
    获取全省/所有分区的最小供电裕度/平均供电裕度, 有供电缺口时长/供电紧张时长
    """
    url = f"{base_url}/zone/all/psm"
    params = {"case_id": case_id}
    resp = requests.get(url=url, params=params).json()
    print(resp)
    assert resp.get("code") == 200


def test_balance_margin_data(base_url, case_id):
    """
    全年供电缺口/调峰缺口信息的年-月-日,月-日-小时分布--单位:万千瓦;
    """
    url = f"{base_url}/balance/margin/data"
    params = {"case_id": case_id}
    resp = requests.get(url=url, params=params).json()
    print(resp)
    assert resp.get("code") == 200


def test_electric_supply_type(base_url, case_id):
    """
    获取供电形式分布特征
    """
    url = f"{base_url}/electric/supply/type"
    params = {"case_id": case_id}
    resp = requests.get(url=url, params=params).json()
    print(resp)
    assert resp.get("code") == 200


def test_zone_gap_code(base_url, case_id):
    """
    获取分区供电缺口的二维码接口
    """
    url = f"{base_url}/zone/gap/code"
    params = {"case_id": case_id}
    resp = requests.get(url=url, params=params).json()
    print(resp)
    assert resp.get("code") == 200


def test_electric_supply_demand(base_url, case_id):
    """
    获取区域/分区的周期内供电缺口小时的0-23点小时分布
    """
    url = f"{base_url}/electric/supply/demand"
    params = {"case_id": case_id}
    resp = requests.get(url=url, params=params).json()
    print(resp)
    assert resp.get("code") == 200


def test_electric_zone_hour_gap(base_url, case_id):
    """
    获取所有分区的某时段内的供电裕度曲线
    """
    url = f"{base_url}/electric/zone/hour/gap"
    params = {"case_id": case_id, "time_no": "2025-12-27 14:00:00"}
    resp = requests.get(url=url, params=params).json()
    print(resp)
    assert resp.get("code") == 200


def test_device_reload_list(base_url, case_id):
    """
    重载风险设备列表
    """
    url = f"{base_url}/device/reload/list"
    params = {"case_id": case_id}
    resp = requests.get(url=url, params=params).json()
    print(resp)
    assert resp.get("code") == 200


def test_get_sys_simulation_boundary(base_url, case_id):
    """
    获取机组装机/设备数目等信息;
    """
    url = f"{base_url}/tmp/simu_boundary"
    params = {"case_id": case_id}
    resp = requests.get(url=url, params=params).json()
    print(resp)
    assert resp.get("code") == 200


def test_get_time_tide_map(base_url, case_id):
    """
    获取时序潮流分析中间的地图数据
    """
    url = f"{base_url}/time/tide/map"
    params = {"case_id": case_id}
    resp = requests.get(url=url, params=params).json()
    print(resp)
    assert resp.get("code") == 200


def test_stable_inference(base_url, case_id):
    """
    暂稳推理
    """
    url = f"{base_url}/stable/inference"
    params = {
        "case_id": case_id,
        "stime": "2025-01-01",
        "etime": "2025-12-31",
        "models": {
            "XGB": ["js2025_v250307"]
        },
        "mask_fault": False
    }
    resp = requests.post(url=url, json=params).json()
    print(resp)
    assert resp.get("code") == 200


def test_stable_day_detail(base_url, case_id):
    """
    获取暂稳推理单日详情数据
    """
    url = f"{base_url}/stable/day/detail"
    params = {"date": "2025-01-01"}
    resp = requests.post(url=url, json=params).json()
    print(resp)
    assert resp.get("code") == 200


def test_stable_day_curve(base_url, case_id):
    """
    获取暂稳推理单日详情数据-电压失稳（失稳节点列表，电压曲线），功角失稳（机组出力曲线、功角失稳曲线）
    """
    url = f"{base_url}/stable/day/curve"
    params = {
        "case_id": case_id,
        "date": "2025-07-21",
        "time_index": 4833,
        "from_bus": "国阳城__525",
        "to_bus": "国东明__525",
        "fault_type": "N-2",
        "target": "tsi",
        "mask_fault": False
    }
    resp = requests.post(url=url, json=params).json()
    print(resp)
    assert resp.get("code") == 200


def test_stable_day_statistics(base_url, case_id):
    """
    获取失稳类型统计
    """
    url = f"{base_url}/stable/day/statistics"
    params = {"type": 1, "mask_fault": False}
    resp = requests.post(url=url, json=params).json()
    print(resp)
    assert resp.get("code") == 200
