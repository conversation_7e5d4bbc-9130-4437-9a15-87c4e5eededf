import os
import signal
import subprocess
import sys
import time

import pytest
import requests


def pytest_configure(config):
    # 动态设置环境变量
    os.environ["bgenv"] = "jiangsu"
    if sys.platform == "darwin":
        os.environ["tsa_base_path"] = "/Volumes/tode_data"


@pytest.fixture(scope="session", autouse=True)
def base_url():
    print("开始执行")
    process = subprocess.Popen(["python", "main.py"])
    time.sleep(30)
    pid = process.pid
    print(f"子进程id: {pid}, 父进程id: {os.getppid()}")
    yield "http://127.0.0.1:8000/api/v1/jiangsu"
    os.kill(pid, signal.SIGKILL)
    print("执行结束")


@pytest.fixture(scope="session", autouse=True)
def case_id(base_url):
    url = f"{base_url}/case/all"
    resp = requests.get(url).json()
    return resp["data"][0].get("id")
