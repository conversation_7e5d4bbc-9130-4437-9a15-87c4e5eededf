import json
import os
import threading
from concurrent.futures import ThreadPoolExecutor

base_path = os.path.dirname(os.path.dirname(__file__))
db_base_path = os.path.join(base_path, 'db')
if not os.path.exists(db_base_path):
    os.makedirs(db_base_path, exist_ok=True)
# 文件存放路径
file_storage = os.path.join(db_base_path, 'files')
# 临时文件存放路径
file_tmp_storage = os.path.join(file_storage, 'tmp')

# 下载分区裕度计算数据目录，方便多个文件进行打包
download_area_dirs = os.path.join(file_tmp_storage, "area_ele_inds")
if not os.path.isdir(download_area_dirs):
    os.makedirs(download_area_dirs, exist_ok=True)

# 数据库---h5文件的key
db_key = 'baogong'
# 数据库文件路径（算例）
db_path = os.path.join(db_base_path, 'data.json')
# 数据库文件(中短期算例，仅限河南存在)
db_short_file = os.path.join(db_base_path, 'short.json')
# network数据存储路径
db_network_dir = os.path.join(db_base_path, 'network')
# 创建network目录
if not os.path.isdir(db_network_dir):
    os.makedirs(db_network_dir, exist_ok=True)

# 系统配置文件路径
db_system_path = os.path.join(db_base_path, 'system.json')

# result_output数据存储路径
db_output_dir = os.path.join(db_base_path, 'output')
# 创建output目录
if not os.path.isdir(db_output_dir):
    os.makedirs(db_output_dir, exist_ok=True)

# ptdf数据存储路径
db_ptdf_dir = os.path.join(db_base_path, 'ptdf')
# 创建output目录
if not os.path.isdir(db_ptdf_dir):
    os.makedirs(db_ptdf_dir, exist_ok=True)

# 检查目录files是否存在，若不存在，则提前创建
if not os.path.isdir(file_storage):
    os.makedirs(file_storage, exist_ok=True)
# 检查files目录下的tmp目录是否存在，若不存在，则提前创建
if not os.path.isdir(file_tmp_storage):
    os.makedirs(file_tmp_storage, exist_ok=True)

# 算例分析日志文件目录
case_logs_dir = os.path.join(db_base_path, 'logs')
# 创建算例分析日志文件目录
if not os.path.isdir(case_logs_dir):
    os.makedirs(case_logs_dir, exist_ok=True)

# cal_result
cal_result_path = os.path.join(db_base_path, 'cal_result')
if not os.path.isdir(cal_result_path):
    os.makedirs(cal_result_path, exist_ok=True)
# case_info
case_info_path = os.path.join(db_base_path, 'case_info')
if not os.path.isdir(case_info_path):
    os.makedirs(case_info_path, exist_ok=True)

# indicator
indicator_path = os.path.join(db_base_path, 'indicator')
if not os.path.isdir(indicator_path):
    os.makedirs(indicator_path, exist_ok=True)

case_dict = {}  # 算例缓存对象信息
short_case_dict = {}  # 短期算例缓存对象信息，仅限河南
case_year_dict = []  # 年份结果集

# 创建线程池
pool = ThreadPoolExecutor(max_workers=10)
# 创建一把线程锁
lock = threading.RLock()

JIANGSU_TAG = 'jiangsu'
HENAN_TAG = 'henan'
HUNAN_TAG = 'hunan'
JIYUAN_TAG = 'jiyuan'
GANSU_TAG = 'gansu'

# 配置文件存储路径
db_config_path = os.path.join(db_base_path, 'config')
# 创建配置文件目录
if not os.path.isdir(db_config_path):
    os.makedirs(db_config_path)

# 配置登陆信息文件
db_login = os.path.join(db_base_path, 'login.json')
# 创建登陆信息文件
if not os.path.isfile(db_login):
    with open(db_login, 'w') as fp:
        fp.write(json.dumps({}, indent=4, ensure_ascii=False))

LOGIN_USER = 'admin'
LOGIN_PASSWORD = 'baogong@)@$'
LOGIN_EXPIRE = 1  # 用户登陆过期时间，默认位1天，也就是次日凌晨1点

allow_api = [
    '/api/v1/common/login', '/api/v1/common/case/download', '/api/v1/common/auto/analyze/case',
    '/api/v1/common/gen/maintenance/download'
]

# 部署省份
bgenv = os.getenv("bgenv")

map_model_field = {"DNN": "深度神经网络", "ICNN": "输入凸神经网络", "CNN": "卷积神经网络", "LGB": "LGB", "XGB": "XGBoost"}

tsa_base_path = os.getenv("tsa_base_path", "/mnt/tode_data")

tsi_model_path = os.path.join(tsa_base_path, "TSA", "data", "model", "tsi")
tvsi_model_path = os.path.join(tsa_base_path, "TSA", "data", "model", "tvsi")
# 故障集列表
fault_set_json = os.path.join(base_path, "tsa", "data", "fault_set.json")
tas_analyzer_mark_true = None
tas_analyzer_mark_false = None
ai_stability_mark_true = None
ai_stability_mark_false = None
bpa_stability_mark_true = None
bpa_stability_mark_false = None
bpa_dict_mark_true = None
bpa_dict_mark_false = None

# 保存工程投产信息文件
project_production_db_path = os.path.join(db_base_path, "production.json")
# 算例转dat文件工作路径
case_to_dat_path = os.path.join(db_base_path, "case_to_dat")
if not os.path.isdir(case_to_dat_path):
    os.makedirs(case_to_dat_path, exist_ok=True)

# 机组检修结果存放位置
gen_maintenance_path = os.path.join(db_base_path, "gen_maintenance")
if not os.path.isdir(gen_maintenance_path):
    os.makedirs(gen_maintenance_path, exist_ok=True)

# 算例统计数据，临时工作目录
case_statistic_path = os.path.join(db_base_path, "case_statistic")
if not os.path.isdir(case_statistic_path):
    os.makedirs(case_statistic_path, exist_ok=True)

# 短路计算数据
short_file = os.path.join(db_path, "short.h5")
df_short_data = None  # 短路计算加载缓存

# 短路计算结果
short_file = os.path.join(db_base_path, "short.h5")
df_short_data = None  # 短路计算缓存数据

# 短路比计算结果
short_rate_file = os.path.join(db_base_path, "short_rate.h5")
df_short_rate_data = None  # 短路比计算缓存数据
