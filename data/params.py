import datetime as dt
import os
import uuid
from dataclasses import dataclass
from dataclasses import field
from typing import Dict
from typing import List
from typing import Optional
from typing import Union

import pandas as pd

import common


@dataclass
class GuiHuaTaiParams:
    case_id: str
    tab_name: List[str]


# 新增算例
@dataclass
class SaveCaseParams:
    name: str  # 算例名称
    filename: str  # 文件名称
    id: str  # 文件key
    year: int  # 年份
    content_type: str  # 文件类型
    start_time: str  # 开始时间
    size: int = 0  # 文件大小
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南
    comment: Union[str, int] = ''  # 备注
    show: bool = True  # 是否显示
    predict: List = field(default_factory=list)  # 预测设置， 仅限河南保供才有此字段
    analysis_stime: Union[str, dt.datetime] = ""  # 分析开始时间
    analysis_etime: Union[str, dt.datetime] = ""  # 分析结束时间
    gen_status: int = 1  # 1: 未检修，2: 检修中，3: 已检修，4:没有需要检修的机组，5:检修失败

    def __post_init__(self):
        # 此处做参数校验
        for field in self.__dict__.keys():
            v = getattr(self, field)
            if isinstance(v, str):
                v = v.strip()
                setattr(self, field, v)
        if len(self.name) > 128:
            raise Exception("算例名称长度限制128")
        if len(self.filename) > 512:
            raise Exception("文件名称长度限制512")
        # 检查文件是否存在
        file_name = f"{self.id}_{self.filename}"
        filepath = os.path.join(common.file_tmp_storage, file_name)
        if not os.path.isfile(filepath):
            raise Exception(f"文件路径不存在: {filepath}")
        if self.comment and isinstance(self.comment, int):
            self.comment = str(self.comment)
        # 检查分析开始时间和分析结束时间
        analysis_stime = ""
        analysis_etime = ""
        if self.analysis_stime:
            analysis_stime = dt.datetime.strptime(self.analysis_stime, "%Y-%m-%d %H:%M:%S")
            if analysis_stime.year != self.year:
                raise Exception(f"分析开始时间超出所填年份")
        if self.analysis_etime:
            analysis_etime = dt.datetime.strptime(self.analysis_etime, "%Y-%m-%d %H:%M:%S")
            if analysis_etime.year != self.year:
                raise Exception(f"分析结束时间超出所填年份")
        if analysis_stime and analysis_etime:
            if analysis_stime > analysis_etime:
                raise Exception(f"分析结束时间不可小于分析开始时间")


# 更新算例
@dataclass
class UpdateCaseParams:
    case_id: str  # 算例id
    name: str  # 算例名称
    year: int  # 年份
    start_time: str  # 开始时间
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南
    comment: Union[str, int] = ""  # 备注
    predict: List = field(default_factory=list)  # 预测设置， 仅限河南保供才有此字段
    analysis_stime: Union[str, dt.datetime] = ""  # 分析开始时间
    analysis_etime: Union[str, dt.datetime] = ""  # 分析结束时间

    def __post_init__(self):
        if self.comment and isinstance(self.comment, int):
            self.comment = str(self.comment)
        # 检查分析开始时间和分析结束时间
        analysis_stime = ""
        analysis_etime = ""
        if self.analysis_stime:
            analysis_stime = dt.datetime.strptime(self.analysis_stime, "%Y-%m-%d %H:%M:%S")
            if analysis_stime.year != self.year:
                raise Exception(f"分析开始时间超出所填年份")
        if self.analysis_etime:
            analysis_etime = dt.datetime.strptime(self.analysis_etime, "%Y-%m-%d %H:%M:%S")
            if analysis_etime.year != self.year:
                raise Exception(f"分析结束时间超出所填年份")
        if analysis_stime and analysis_etime:
            if analysis_stime > analysis_etime:
                raise Exception(f"分析结束时间不可小于分析开始时间")


# 删除算例
@dataclass
class DeleteCaseParams:
    case_id: str  # 算例id
    debug: bool = False
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南


@dataclass
class CaseObjParams:
    case_id: str  # 算例id
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南


@dataclass
class StatusCaseParams:
    cases: List[CaseObjParams] = field(default_factory=list)  # 算例列表


@dataclass
class GetStatusCaseParams:
    case_ids: List[str]  # 算例id列表
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南


# 分析算例
@dataclass
class AnalyzeCaseParams:
    case_id: str  # 算例id
    area: str = 'jiyuan'
    debug: bool = False
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南


# 推演参数
@dataclass
class DeducePowerParams:
    calculate_flag: bool  # 是否需要计算
    case_id: str  # 算例id
    timestep: int  # 推演时刻
    device_name: List  # 故障设备
    feedin: float  # 整个feedin的目标值;
    filter_name: str  # 分区名称
    gas: float  # 燃气机组总目标值
    load: float  # 负荷总目标值
    wind: float  # 风电
    solar: float  # 光伏
    phase_shifter_arg: Dict
    add_reactance_arg: Dict = field(default_factory=dict)
    dc_feedin: Dict = field(default_factory=dict)
    grid_fault: bool = False  # 是否有设备故障
    is_short: int = 1


# 获取指定时刻的断面分析
@dataclass
class DeviceInterfaceParams:
    case_id: str  # 算例id
    time_list: List[int] = field(default_factory=list)


# 启用算例/取消算例
@dataclass
class StartCaseParams:
    case_id: str  # 算例id
    status: int  # 算例状态
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南

    def __post_init__(self):
        if self.status not in [2, 4]:
            raise Exception("状态参数错误")


# 获取时序数据
@dataclass
class getSequenceTimeParams:
    case_id: str  # 算例id
    time_list: List[int] = field(default_factory=list)  # 时刻列表


# 替换算例
@dataclass
class ReplaceCaseFileParams:
    old_id: str  # 旧算例id
    filename: str  # 文件名称
    new_id: str  # 新算例id
    size: int = 0  # 文件大小
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南

    def __post_init__(self):
        # 此处做参数校验
        for field in self.__dict__.keys():
            v = getattr(self, field)
            if isinstance(v, str):
                v = v.strip()
                setattr(self, field, v)
        if len(self.filename) > 512:
            raise Exception("文件名称长度限制512")
        # 检查文件是否存在
        file_name = f"{self.new_id}_{self.filename}"
        filepath = os.path.join(common.file_tmp_storage, file_name)
        if not os.path.isfile(filepath):
            raise Exception(f"文件路径不存在: {filepath}")


# 登陆
@dataclass
class LoginParams:
    username: str  # 用户名
    password: str  # 密码

    def __post_init__(self):
        self.password = self.password.strip()
        if len(self.password) < 6 or len(self.password) > 20:
            raise Exception(f"密码长度限制为6到20位")


# 登陆信息校验
@dataclass
class LoginValidParams:
    token: str  # token


# 获取设备信息
@dataclass
class CurevDeviceInfoParams:
    case_id: str
    device: Dict


# 获取时序潮流断面信息
@dataclass
class GetTimeTideSurfaceInfo:
    case_id: str  # 算例id
    ele_type: str  # "trafo","line","interface","channel"
    ele_dict: Dict  # 带查询设备信息dict
    rate_mk: List = field(default_factory=list)  # 重过载门槛值

    def __post_init__(self):
        if not self.rate_mk:
            self.rate_mk = [0.8, 1.0]


# 获取全省供电缺口时长
@dataclass
class GetPowerSupplyGapTime:
    case_id: str  # 算例id
    func_type: str = "num_count"  # "num_count": 统计个数小时数据,"sum_count";统计和,"max_count"：统计最大值
    value_type: List = field(default_factory=list)

    def __post_init__(self):
        if not self.value_type:
            self.value_type = ['load_curtailment']


# 获取全省储能调用情况
@dataclass
class GetStorageEnergyUseSituation:
    case_id: str  # 算例id
    area: str
    time_list: List  # 2024-12-26 00:00:00
    is_all: bool = False


# 获取最低供电裕度
@dataclass
class GetPowerMinMarginParams:
    case_id: str  # 算例id
    zone_name_list: List = field(default_factory=list)  # such as :["访晋","泰扬北"]
    freq_h: str = "season"  # "分布统计方法","hour": 0-23小时分布; "month":月度分布; "season":季度分布;


# 获取天的直流功率时序数据
@dataclass
class GetPowerDclineSeriesParams:
    case_id: str  # 算例id
    dc_line_idx: List = field(default_factory=list)
    is_all: bool = False
    time_list: List = field(default_factory=list)


# 获取电网拓扑图
@dataclass
class MapMarginDataParams:
    case_id: str  # 算例id
    time_no: Optional[int] = None  # 时刻
    area: str = "全省"


@dataclass
class ZoneAllIndicatorParams:
    case_id: str  # 算例id
    time_no: int  # 时刻
    area: str = "全省"


@dataclass
class ZoneTrafoCapabilityParams:
    case_id: str  # 算例id
    time_no: int  # 时刻
    area: str = "全省"


# 获取关联通道
@dataclass
class ChannelRelationInfoParams:
    case_id: str  # 算例id
    ele_dict: Dict = field(default_factory=dict)


@dataclass
class GetNetworkTopoAllParams:
    case_id: str  # 算例id
    time_no: int  # 时刻


@dataclass
class MapNetworkDataParams:
    case_id: str
    time_no: Optional[int] = None
    value_type: Optional[str] = None
    area: str = "全省"


# 暂稳推理
@dataclass
class StableInferenceParams:
    case_id: str  # 算例id
    stime: str  # 开始时间
    etime: str  # 结束时间
    models: Dict[str, List[str]]  # 用户选择的模型列表
    mask_fault: bool = False


# 地图参数
@dataclass
class StableMapDataParams:
    case_id: str  # 算例id


# 单日详情
@dataclass
class StableDayDetailParams:
    date: str  # 日期
    type: int = 1  # 1: 人工智能，2:仿真
    mask_fault: bool = False


# 单日暂稳分析数据
@dataclass
class StableDayAnalyzeParams:
    date: str  # 日期
    time_no: int  # 时间点

    def __post_init__(self):
        year = self.date.split("-")[0]
        target_times = pd.date_range(
            start=f'{self.date} 00:00:00', end=f'{self.date} 23:00:00', freq='H'
        ).astype(str).tolist()
        # 使用date_range函数生成时间序列
        all_times = pd.date_range(
            start=f'{year}-01-01 00:00:00', end=f'{year}-12-31 23:00:00', freq='H'
        ).astype(str).tolist()
        time_list = []
        for t in target_times:
            index = all_times.index(t)
            time_list.append(index)
        self.time_no = time_list[self.time_no - 1]


# 获取新能源的发电同时率;常规机组的开机容量、以及负荷;
@dataclass
class StableNewrateCapacityParams:
    case_id: str  # 算例id
    date: str  # 日期
    time_list: List[int] = field(default_factory=list)

    def __post_init__(self):
        year = self.date.split("-")[0]
        target_times = pd.date_range(
            start=f'{self.date} 00:00:00', end=f'{self.date} 23:00:00', freq='H'
        ).astype(str).tolist()
        # 使用date_range函数生成时间序列
        all_times = pd.date_range(
            start=f'{year}-01-01 00:00:00', end=f'{year}-12-31 23:00:00', freq='H'
        ).astype(str).tolist()
        for t in target_times:
            index = all_times.index(t)
            self.time_list.append(index)


# 单日详情-电压失稳（失稳节点列表、电压曲线），功角失稳（机组出力曲线、功角失稳曲线）
@dataclass
class StableDayCurveParams:
    case_id: str
    time_index: int
    from_bus: str
    to_bus: str
    fault_type: str
    target: str  # tvsi: 电压失稳，tsi：功角失稳
    date: Optional[str] = None  # 日期
    mask_fault: bool = False


# 通过线路获取失稳详情
@dataclass
class StableDetailByLineParams:
    from_bus: str
    to_bus: str
    type: int = 1  # 1: 人工智能，2：仿真
    mask_fault: bool = False


# 获取失稳类型统计
@dataclass
class StableDayStatisticsParams:
    type: int = 1  # 1: 人工智能，2：仿真
    mask_fault: bool = False


# 设置是否显示算例
@dataclass
class ShowCaseParams:
    case_id: str  # 算例id
    show: bool  # 算例状态
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南


# 新增工程
@dataclass
class AddProjectParams:
    name: str  # 名称
    capacity: Union[int, float]  # 容量
    num: int  # 台数
    type: str  # 项目类型
    area: str  # 所属分区
    year: int  # 所属年份
    plan: str  # 计划
    optimize: str  # 优化
    dispname: str  # 简称
    order: int = 1  # 排序
    id: str = ""

    def __post_init__(self):
        self.id = str(uuid.uuid4())


# 编辑工程
@dataclass
class EditProjectParams:
    id: str
    name: str  # 名称
    capacity: Union[int, float]  # 容量
    num: int  # 台数
    type: str  # 项目类型
    area: str  # 所属分区
    year: int  # 所属年份
    plan: str  # 计划
    optimize: str  # 优化
    dispname: str  # 简称
    order: int = 1  # 排序


# 删除工程
@dataclass
class DeleteProjectParams:
    id: str


# 开始机组检修
@dataclass
class StartGenMaintenance:
    case_id: str
    is_maintenance: bool
    debug: bool = False


# 取消机组检修
@dataclass
class GenMaintenanceCancel:
    case_id: str


# 算例转dat
@dataclass
class CaseToDatParams:
    case_id: str


# 算例统计数据
@dataclass
class CaseStatisticParams:
    case_id: str


# 获取厂站列表
@dataclass
class ShortStationListParams:
    case_id: str
    area: str = "全省"
    year: int = 2025
    page: int = 1
    size: int = 20
    date: Optional[str] = None  # 日期， 示例：2025-01-01
    time_idx: Optional[List[int]] = None

    def __post_init__(self):
        if self.date:
            date = pd.Timestamp(self.date)
            day_of_year = date.dayofyear
            self.time_idx = [(day_of_year - 1) * 24 + h for h in range(24)]


# 电路电流-地图数值
@dataclass
class ShortMapValueParams:
    case_id: str
    area: str = "全省"


# 电路电流-开机容量
@dataclass
class ShortGenCapacityParams:
    case_id: str
    area: str = "全省"
    date: Optional[str] = None  # 日期
    time_idx: Optional[List[int]] = None

    def __post_init__(self):
        if self.date:
            date = pd.Timestamp(self.date)
            day_of_year = date.dayofyear
            self.time_idx = [(day_of_year - 1) * 24 + h for h in range(24)]


# 电路电流-分区主变容量
@dataclass
class ShortAreaTrafoParams:
    case_id: str
    area: str = "全省"
