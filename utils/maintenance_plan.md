# 机组检修计划安排 (`run_maintenance_plan`) 说明文档

本文档详细说明了 `utils/gen_maintenance.py` 脚本中 `run_maintenance_plan` 函数的输入、输出格式以及其核心算法执行流程。

## 1. 函数目标

该函数旨在为电力系统中的发电机组安排年度检修计划。其核心优化目标是：在安排所有必要的机组检修后，使得各个电力分区以及全系统（或全省）的逐小时供电裕度曲线的年度总方差尽可能小。

## 2. 输入格式

函数需要三个 Pandas DataFrame作为输入：

### 2.1 `gen_df: pd.DataFrame` - 发电机组属性表

该 DataFrame 包含每个发电机组的详细信息。

**必需列：**

*   **索引**: 机组的唯一标识符（例如，机组ID或名称）。在与 `zone_df` 合并时会用到。
*   `name` (str): 机组的简称或代码。
*   `max_p_mw` (float/int): 机组的最大出力（单位：兆瓦 MW）。
*   `maintenance_day` (float/int): 机组年度计划检修天数。
*   `in_service` (bool): 机组是否在役。`True` 表示在役，`False` 表示退役或不可用。只有 `in_service` 为 `True` 且 `maintenance_day` 大于0的机组才会被安排检修。

**可选列（推荐包含）：**

*   `fullname` (str): 机组的全名，用于更详细的输出和日志记录。如果缺少，将使用 `name` 列。
*   `分区名` (str): 如果 `gen_df` 本身已包含机组所属分区信息，并且不打算使用 `zone_df` 进行映射，则此列可直接提供分区名。

### 2.2 `margin_df: pd.DataFrame` - 供电裕度表

该 DataFrame 代表了在未安排任何新增检修情况下，全年8760小时（或其他分析周期）的系统及各分区的供电裕度。

**格式：**

*   **索引**: 代表一年中的小时序号（例如，从0到8759）。
*   **列**:
    *   每一列代表一个电力分区的名称（字符串类型，例如 "分区A", "分区B"）。这些分区名应与 `gen_df`（经 `zone_df` 映射后）中的 `分区名` 一致。
    *   必须包含一列代表全系统（或全省）的总供电裕度。此列的名称应为 **"全省"** 或 **"全系统"**。函数会优先查找 "全省"，如果未找到，则查找 "全系统"。
*   **值**: 对应小时的供电裕度（数值类型，单位通常为MW）。

### 2.3 `zone_df: pd.DataFrame` - 机组分区映射表

该 DataFrame 用于将 `gen_df` 中的机组映射到其所属的电力分区。

**格式：**

*   **索引**: 与 `gen_df` 中机组索引对应，用于匹配机组。
*   **列**:
    *   `分区名` (str): 包含机组所属分区的名称。此名称必须与 `margin_df` 中的分区列名对应。

**合并逻辑：** `gen_df` 会与 `zone_df` 进行左合并（left merge），以 `gen_df` 的索引为基准。这意味着 `gen_df` 中的所有机组都会被保留。如果某个机组在 `zone_df` 中没有对应的分区信息，其 `分区名` 将为 `NaN`。

## 3. 输出格式

函数返回一个 Pandas DataFrame (`final_maintenance_plan_df`)，包含了所有被安排检修机组的详细计划。

**列定义：**

*   `name` (str): 机组简称。
*   `fullname` (str): 机组全名。
*   `max_p_mw` (float/int): 机组最大出力。
*   `maintenance_day_duration` (float/int): 机组的原始计划检修天数。
*   `zone` (str): 机组所属分区名。如果机组无有效分区信息，则为 "N/A"。
*   `maintenance_start_hour_idx` (int): 检修开始的小时索引（0-8759）。
*   `maintenance_end_hour_idx` (int): 检修结束的小时索引（0-8759，包含此小时）。
*   `maintenance_duration_hours` (int): 实际安排的检修持续小时数（由 `maintenance_day` 四舍五入后乘以24得到）。
*   `maintenance_start_day` (int): 检修开始的日期（一年中的第几天，从1开始计数）。
*   `maintenance_end_day` (int): 检修结束的日期（一年中的第几天，从1开始计数，包含此天）。

## 4. 算法执行过程（优化版）

算法采用贪心策略，逐个为满足条件的机组安排最优的检修时段。

**核心步骤：**

1.  **初始化与数据准备：**
    *   打印开始执行信息。
    *   **数据合并**：将 `gen_df` 与 `zone_df` 进行左合并，得到 `gen_df_merged`，确保所有机组信息得到保留，并附加上分区信息（可能为NaN）。
    *   **裕度数据校验**：
        *   确认 `margin_df` 中存在代表全系统/全省的裕度列（优先 "全省"，其次 "全系统"）。
        *   校验 `gen_df_merged` 中出现的所有有效分区名是否都在 `margin_df` 的列中。
    *   复制 `margin_df` 为 `current_margin_df`，后续实际的裕度削减将在此副本上操作。
    *   获取年度总小时数 `N` (即 `len(margin_df)`).
    *   **计算初始统计量**：对于 `current_margin_df` 中的每一列（每个分区及全系统/全省），计算其初始的数值总和 (S1) 和数值平方的总和 (S2)。这些统计量将用于后续高效计算方差。存储在字典 `current_S1s` 和 `current_S2s` 中。
    *   打印相关初始化信息。

2.  **筛选待检修机组：**
    *   从 `gen_df_merged` 中筛选出满足以下条件的机组：
        *   `in_service == True` (在役机组)
        *   `maintenance_day > 0` (需要检修)
    *   将筛选出的机组按 `max_p_mw` (最大出力) **降序**排列。优先安排大机组的检修，因为它们对系统裕度的影响更大，早期确定其位置有助于后续小机组在更优化的全局状态下进行选择。
    *   打印筛选出的待检修机组数量。如果数量为0，则直接返回空的检修计划。

3.  **迭代安排机组检修（外层循环 - 逐机组）：**
    *   按排序逐个处理待检修机组。对于每个机组：
        *   获取机组的 `max_p` (出力)、`maintenance_hours` (检修小时数，由检修天数换算得到)、`zone_name_val` (分区名)。
        *   打印当前正在处理的机组信息。
        *   进行有效性检查（如出力、检修小时数是否有效）。
        *   **确定受影响列**：确定此机组检修会影响哪些裕度列（即全系统/全省列，以及其所属分区列，如果分区有效且存在于 `margin_df` 中）。
        *   **初始化滑动窗口**：对于每个受影响的列，计算在 `current_margin_df` 上，从第0小时开始、长度为 `maintenance_hours` 的第一个窗口内裕度值的总和。这用于后续滑动计算。

4.  **寻找最佳检修时段（内层循环 - 逐时段）：**
    *   对于当前机组，遍历所有可能的检修起始小时 `start_h_candidate`（从0到 `N - maintenance_hours`）。
        *   **复制统计量**：复制当前的 `current_S1s` 和 `current_S2s` 到临时的 `temp_S1s` 和 `temp_S2s`。
        *   **更新滑动窗口和**：如果不是第一个候选时段 (`start_h_candidate > 0`)，则通过减去离开窗口的值并加上新进入窗口的值，来高效更新每个受影响列的滑动窗口内裕度值总和 (`sum_X_orig_window`)。
        *   **模拟检修影响（增量计算S1, S2）**：对于每个受影响的列：
            *   获取当前窗口的 `sum_X_orig_window`。
            *   计算由于在该窗口内减去 `max_p` 所导致的 `delta_S1` 和 `delta_S2`：
                *   `delta_S1 = -max_p * maintenance_hours`
                *   `delta_S2 = -2 * max_p * sum_X_orig_window + maintenance_hours * (max_p^2)`
            *   更新 `temp_S1s[col] += delta_S1` 和 `temp_S2s[col] += delta_S2`。
        *   **计算总模拟方差**：使用更新后的 `temp_S1s` 和 `temp_S2s`，计算 `margin_df` 中所有列（不仅仅是直接受影响的列）的模拟方差，并求和得到 `current_simulated_total_variance`。方差计算公式为 `Var(X) = (S2/N) - (S1/N)^2`。
        *   **记录最优解**：如果 `current_simulated_total_variance` 小于当前机组已找到的 `min_total_variance_for_gen`，则更新最小方差，记录当前的 `start_h_candidate` 为 `best_start_hour_for_gen`，并保存此时各受影响列的 `sum_X_orig_window` 值（用于后续永久更新S1/S2）。

5.  **确定并应用检修计划：**
    *   在遍历完当前机组所有可能的检修时段后，如果找到了 `best_start_hour_for_gen` (即成功找到一个优化时点)：
        *   打印找到的最佳检修开始小时和对应的方差。
        *   **永久更新统计量**：使用记录的、对应最佳检修窗口的 `sum_X_orig_window_chosen` 值，为每个受影响的列计算 `delta_S1_chosen` 和 `delta_S2_chosen`，并用它们**永久更新** `current_S1s` 和 `current_S2s`。
        *   **实际修改裕度**：在 `current_margin_df` 中，对于最佳检修时段 (`chosen_start_hour` 到 `chosen_end_hour`)，将受影响列的裕度值减去 `max_p`。
        *   **记录检修信息**：将该机组的检修安排（包括名称、出力、起止小时、起止天数等）添加到 `maintenance_plan_records` 列表中。
        *   打印机组检修已安排的信息。
    *   如果未能找到优化时段（理论上在当前逻辑下不太可能发生，除非检修时间过长或数据异常），则打印警告信息。

6.  **生成并返回结果：**
    *   所有待检修机组处理完毕后，打印完成信息。
    *   将 `maintenance_plan_records` 列表转换为 Pandas DataFrame (`final_maintenance_plan_df`)。
    *   打印最终检修计划包含的记录数。
    *   **清理**：恢复输入 `gen_df` 和 `zone_df` 可能被临时修改的索引名。
    *   打印函数执行完毕信息。
    *   返回 `final_maintenance_plan_df`。

**性能优化说明：**

与朴素的每次模拟都复制整个裕度表并重新计算方差相比，此优化版算法的核心优势在于：
*   **增量计算**：通过维护S1和S2统计量，避免了对整个数据集的重复扫描来计算方差。
*   **滑动窗口**：在寻找最佳检修时段时，窗口内原始数据和的计算是O(1)的（初始化后），而不是O(检修时长)。
*   **最小化复制**：主要的数据复制仅限于函数开始时的一次 `margin_df` 复制，以及内层循环中对S1/S2字典（小数据结构）的复制。

这些优化显著降低了计算复杂度，从而大幅提高了算法的执行效率，尤其是在处理大量机组和长分析周期时。