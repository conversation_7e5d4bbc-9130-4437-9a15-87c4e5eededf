import os
import shutil

from apscheduler.schedulers.background import BackgroundScheduler
from loguru import logger

import common

# 创建一个scheduler实例
sched = BackgroundScheduler()


def clear_directory(directory_path: str):
    try:
        # 如果目录不存在，则直接返回
        if not os.path.exists(directory_path):
            return

        # 遍历目录中的所有文件和子目录
        for item in os.listdir(directory_path):
            item_path = os.path.join(directory_path, item)
            # 如果是文件，则删除文件
            if os.path.isfile(item_path):
                os.remove(item_path)
            # 如果是子目录，则递归清空子目录
            elif os.path.isdir(item_path):
                shutil.rmtree(item_path)

        logger.info(f"目录{directory_path}已清空。")
    except Exception as e:
        logger.info(f"清空目录时出现错误：{e}")


# 定时清空下载分区裕度的目录
@sched.scheduled_job('cron', hour=0, minute=30)
def clear_dirs_file():
    logger.info(f"开始清理下载分区裕度的临时目录")
    clear_directory(common.download_area_dirs)
    logger.info(f"完成清理下载分区裕度的临时目录")
