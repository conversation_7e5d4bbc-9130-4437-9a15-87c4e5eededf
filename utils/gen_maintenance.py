import numpy as np
import pandas as pd


def run_maintenance_plan(
    gen_df: pd.DataFrame,
    margin_df: pd.DataFrame,
    zone_df: pd.DataFrame,
) -> pd.DataFrame:
    """
    按供电裕度执行机组的分区检修计划的安排，优化目标：分区和全省的供电裕度的方差尽可能小
    Args:
        gen_df: 发电机组df, 包含机组名称、区域、发电能力等信息
        margin_df: 各区域的供电裕度df, 包含区域名称、供电裕度等信息
        zone_df: 区域映射关系df, 用于将机组分配到对应的区域
    Returns:
        pd.DataFrame: 优化后的机组检修计划数据框，包含机组名称、区域、发电能力等信息
    """

    print("开始执行机组检修计划安排 run_maintenance_plan (优化版)...")
    margin_df = margin_df * 10  # margin_df 的单位是 万千瓦，乘以10转化为 MW
    # 1. 合并 gen_df 和 zone_df
    print("步骤 1: 合并机组数据 (gen_df) 与分区数据 (zone_df)...")
    original_gen_index_name = gen_df.index.name
    original_zone_index_name = zone_df.index.name
    _gen_df = gen_df.copy()
    _zone_df = zone_df.copy()
    if not _gen_df.index.name:
        _gen_df.index.name = "gen_id_temp_for_merge"
    if not _zone_df.index.name:
        _zone_df.index.name = "gen_id_temp_for_merge"
    gen_df_merged = pd.merge(_gen_df, _zone_df, left_index=True, right_index=True, how='left')
    if '分区名' not in gen_df_merged.columns:
        gen_df_merged['分区名'] = np.nan
        print("  '分区名' 列在 zone_df 中不存在或未成功合并，已创建并填充为 NaN。")
    print(f"  合并完成。合并后 gen_df_merged 行数: {len(gen_df_merged)}")
    print("步骤 1 完成。")

    # 2. 校验裕度数据 (margin_df) 和初始化统计量
    print("步骤 2: 校验裕度数据并初始化统计量...")
    system_wide_col_name = "全省"
    margin_cols_set = set(margin_df.columns)
    if system_wide_col_name not in margin_cols_set:
        system_wide_col_name_alt = "全系统"
        if system_wide_col_name_alt in margin_cols_set:
            system_wide_col_name = system_wide_col_name_alt
            print(f"  警告: margin_df 中未找到列 '全省'，已改用备选列名 '{system_wide_col_name}'。")
        else:
            raise ValueError(f"margin_df 中必须包含表示系统总体的列，尝试过 '全省' 和 '全系统' 但均未找到。")

    valid_gen_zones = gen_df_merged['分区名'].dropna().unique()
    for zone in valid_gen_zones:
        if zone not in margin_cols_set:
            raise ValueError(f"机组分区 '{zone}' 在 margin_df 的列中未找到。")
    print(f"  裕度数据校验通过。全系统/全省列名: '{system_wide_col_name}'。")

    current_margin_df = margin_df.copy()  # 用于实际修改裕度
    N = num_hours = len(margin_df)
    print(f"  年度总小时数: {N}")

    # 初始化S1 (sum) 和 S2 (sum of squares), S1: 供电裕度累积和, S2: 供电裕度平方和
    current_S1s = {col: current_margin_df[col].sum() for col in current_margin_df.columns}
    current_S2s = {col: (current_margin_df[col]**2).sum() for col in current_margin_df.columns}
    print("  初始 S1 和 S2 计算完毕。")
    print("步骤 2 完成。")

    maintenance_plan_records = []

    # 3. 筛选并排序待检修机组
    print("步骤 3: 筛选并排序待检修机组...")
    required_cols_gen = ['name', 'max_p_mw', 'maintenance_day', 'in_service']
    for col in required_cols_gen:
        if col not in gen_df_merged.columns and not (col == 'fullname' and col not in gen_df_merged.columns):
            raise ValueError(f"输入 gen_df (合并后) 缺少必需列: '{col}'")

    gens_to_schedule_df = gen_df_merged[(gen_df_merged['in_service'] == True)
                                        & (gen_df_merged['maintenance_day'] > 0)].copy()
    gens_to_schedule_df.sort_values(by='max_p_mw', ascending=False, inplace=True)
    num_gens_to_schedule = len(gens_to_schedule_df)
    print(f"  筛选完成。共 {num_gens_to_schedule} 台机组需要安排检修。")
    if num_gens_to_schedule == 0:
        print("  没有需要安排检修的机组。返回空计划。")
        return pd.DataFrame(maintenance_plan_records)
    print("步骤 3 完成。")

    # 4. 迭代安排检修
    print("步骤 4: 开始迭代安排机组检修...")
    scheduled_count = 0
    for gen_idx, gen_row in gens_to_schedule_df.iterrows():
        scheduled_count += 1
        gen_name_primary = gen_row['name']
        gen_fullname = gen_row.get('fullname', gen_name_primary)
        max_p = gen_row['max_p_mw']
        maintenance_days_float = gen_row['maintenance_day']
        zone_name_val = gen_row['分区名']
        maintenance_plan_records.append(
            {
                "gen_idx": gen_idx,
                'name': gen_name_primary,
                'fullname': gen_fullname,
                'max_p_mw': max_p,
                'maintenance_day_duration': maintenance_days_float,
                'zone': zone_name_val if pd.notna(zone_name_val) else "N/A",
                'maintenance_start_hour_idx': None,  # 将在后续步骤中填充
                'maintenance_end_hour_idx': None,  # 将在后续步骤中填充
                'maintenance_duration_hours': None,  # 将在后续步骤中填充
                'maintenance_start_day': None,  # 将在后续步骤中填充
                'maintenance_end_day': None  # 将在后续步骤中填充
            }
        )

        print(
            f"  处理机组 ({scheduled_count}/{num_gens_to_schedule}): {gen_fullname} (Pmax: {max_p} MW, Days: {maintenance_days_float}, Zone: {zone_name_val if pd.notna(zone_name_val) else 'N/A'})"
        )

        if pd.isna(max_p) or max_p <= 0:
            print(f"    跳过: Pmax无效 ({max_p})")
            continue
        if pd.isna(maintenance_days_float) or maintenance_days_float <= 0:
            print(f"    跳过: 检修天数无效 ({maintenance_days_float})")
            continue

        # 检修天数向上取整到完整天，然后转换为小时
        # maintenance_days_rounded = int(np.ceil(maintenance_days_float)) # 如果要求必须覆盖，则用ceil
        maintenance_days_rounded = int(round(maintenance_days_float))  # 四舍五入到最近的整数天
        if maintenance_days_rounded <= 0:
            print(f"    跳过: 四舍五入后的检修天数 ({maintenance_days_rounded}) 无效")
            continue

        m_h = maintenance_hours = maintenance_days_rounded * 24

        if m_h <= 0:  # 理论上已被上面覆盖
            print(f"    跳过: 检修小时数无效 ({m_h})")
            continue
        if m_h > N:
            print(f"    跳过: 检修小时 ({m_h}) > 总小时 ({N})")
            continue

        best_start_hour_for_gen = -1
        min_total_variance_for_gen = float('inf')
        best_sum_X_orig_window_map_for_gen = {}

        # 确定此机组影响的列
        affected_cols_for_gen = [system_wide_col_name]
        is_zone_valid_and_in_margin = pd.notna(zone_name_val) and zone_name_val in margin_cols_set
        if is_zone_valid_and_in_margin and zone_name_val != system_wide_col_name:  # 避免重复添加
            affected_cols_for_gen.append(zone_name_val)

        # 候选开始时间现在是每天的0点，所以步长是24
        # N - m_h + 1 是总的可能的小时起点数。我们需要确保最后一个窗口的结束不超过N
        # 如果 N=8760, m_h=24*7=168. 最后一个可能的 start_h_candidate 是 8760-168 = 8592.
        # range(0, 8592 + 1, 24)
        for start_h_candidate in range(0, N - m_h + 1, 24):
            end_h_candidate = start_h_candidate + m_h

            min_margin_in_window = current_margin_df[system_wide_col_name].values[
                start_h_candidate:end_h_candidate].min()
            if min_margin_in_window - max_p < 0.:
                # 如果在这个候选窗口内，系统供电裕度的最小值小于0，则跳过这个候选窗口
                print(f"    跳过: 候选开始时间 {start_h_candidate} 供电裕度为负值 ({min_margin_in_window:.2f})")
                continue

            if is_zone_valid_and_in_margin:
                min_margin_in_window_zone = current_margin_df[zone_name_val].values[
                    start_h_candidate:end_h_candidate].min()
                if min_margin_in_window_zone - max_p < 0.:
                    # 如果在这个候选窗口内，分区供电裕度的最小值小于0，则跳过这个候选窗口
                    # print(f"    跳过: 候选开始时间 {start_h_candidate} 分区 {zone_name_val} 供电裕度为负值 ({min_margin_in_window_zone:.2f})")
                    continue

            temp_S1s = current_S1s.copy()
            temp_S2s = current_S2s.copy()
            current_sum_X_orig_window_map_iter = {}

            # 由于 start_h_candidate 按天跳跃，滑动窗口的优化不再适用，
            # 需要为每个候选窗口重新计算 sum_X_orig_window
            for col_name in affected_cols_for_gen:
                # .values[start:end] 比 .iloc[start:end].sum() 更快
                sum_X_orig_window = np.sum(current_margin_df[col_name].values[start_h_candidate:end_h_candidate])
                current_sum_X_orig_window_map_iter[col_name] = sum_X_orig_window

                # S1' = S1 - max_p * m_h
                delta_S1 = -max_p * m_h
                # S2' = S2 - 2 * max_p * sum_X_orig_window + m_h * max_p^2
                delta_S2 = -2 * max_p * sum_X_orig_window + m_h * (max_p**2)

                temp_S1s[col_name] += delta_S1
                temp_S2s[col_name] += delta_S2

            current_simulated_total_variance = 0
            for col_calc_var in current_margin_df.columns:
                s1 = temp_S1s[col_calc_var]
                s2 = temp_S2s[col_calc_var]
                # 确保 N (num_hours) 不为0，尽管在正常情况下它总是8760
                if N == 0:
                    variance = 0  # 或者抛出错误，或者处理为 np.nan
                else:
                    variance = (s2 / N) - (s1 / N)**2
                current_simulated_total_variance += variance

            if current_simulated_total_variance < min_total_variance_for_gen:
                min_total_variance_for_gen = current_simulated_total_variance
                best_start_hour_for_gen = start_h_candidate
                best_sum_X_orig_window_map_for_gen = current_sum_X_orig_window_map_iter.copy()

        if best_start_hour_for_gen != -1:
            chosen_start_hour = best_start_hour_for_gen
            # m_h 已经是按天对齐的小时数
            chosen_end_hour = chosen_start_hour + m_h
            print(
                f"    为机组 {gen_fullname} 找到最佳检修开始小时: {chosen_start_hour} (方差: {min_total_variance_for_gen:.2f})"
            )

            # 永久更新 current_S1s, current_S2s
            for col_name in affected_cols_for_gen:
                sum_X_orig_window_chosen = best_sum_X_orig_window_map_for_gen[col_name]
                delta_S1_chosen = -max_p * m_h
                delta_S2_chosen = -2 * max_p * sum_X_orig_window_chosen + m_h * (max_p**2)
                current_S1s[col_name] += delta_S1_chosen
                current_S2s[col_name] += delta_S2_chosen

            for col_name_to_update_margin in affected_cols_for_gen:
                current_margin_df.iloc[chosen_start_hour:chosen_end_hour,
                                       current_margin_df.columns.get_loc(col_name_to_update_margin)] -= max_p

            maintenance_start_day = (chosen_start_hour // 24) + 1
            maintenance_end_day = ((chosen_end_hour - 1) // 24) + 1
            maintenance_plan_records[-1] = {
                "gen_idx": gen_idx,
                'name': gen_name_primary,
                'fullname': gen_fullname,
                'max_p_mw': max_p,
                'maintenance_day_duration': maintenance_days_float,
                'zone': zone_name_val if pd.notna(zone_name_val) else "N/A",
                'maintenance_start_hour_idx': chosen_start_hour,
                'maintenance_end_hour_idx': chosen_end_hour - 1,
                'maintenance_duration_hours': m_h,
                'maintenance_start_day': maintenance_start_day,
                'maintenance_end_day': maintenance_end_day
            }
            print(f"    机组 {gen_fullname} 检修已安排: 第 {maintenance_start_day} 天 - 第 {maintenance_end_day} 天。")
        else:
            print(f"    警告：未能为机组 {gen_fullname} 找到优化检修时间。")
    print("步骤 4 完成。")

    print("步骤 5: 生成最终检修计划 DataFrame...")
    final_maintenance_plan_df = pd.DataFrame(maintenance_plan_records).set_index('gen_idx').sort_index()
    print(f"  最终检修计划包含 {len(final_maintenance_plan_df)} 条记录。")

    print("步骤 6: 清理与恢复索引名...")
    _gen_df.index.name = original_gen_index_name
    _zone_df.index.name = original_zone_index_name
    if gen_df.index.name == "gen_id_temp_for_merge": gen_df.index.name = original_gen_index_name
    if zone_df.index.name == "gen_id_temp_for_merge": zone_df.index.name = original_zone_index_name
    print("步骤 6 完成。")

    print("机组检修计划安排执行完毕。")
    return final_maintenance_plan_df


def save_and_plot_maintenance_results(
    maintenance_plan_df: pd.DataFrame, margin_df: pd.DataFrame, output_dir: str, region_name: str = '全省'
) -> None:
    """
    保存检修计划到CSV文件，并根据指定区域绘制结果图。

    封装了将检修计划DataFrame保存到CSV文件，并生成一个图表，
    该图表比较了特定区域的计划检修功率与系统裕度。

    Args:
        maintenance_plan_df (pd.DataFrame): 包含检修计划结果的DataFrame。
            预期列包括 'maintenance_start_hour_idx', 'maintenance_end_hour_idx',
            'max_p_mw', 和 'zone'。
        margin_df (pd.DataFrame): 包含系统裕度数据的DataFrame。
            列名应对应于区域名称。
        output_dir (str): 将保存CSV文件的目录路径。
        region_name (str, optional): 要筛选和绘制的区域名称。
            默认为 '全省'，此时将计算所有机组的总检修功率。

    Side Effects:
        - 在 `output_dir` 中保存一个 'maintenance_plan.csv' 文件。
        - 显示一个 matplotlib 绘图窗口。
    """
    import os

    import matplotlib.pyplot as plt

    # 1. 保存完整的检修计划到CSV
    output_path = os.path.join(output_dir, "maintenance_plan.csv")
    maintenance_plan_df.to_csv(output_path, encoding='utf-8-sig')
    print(f"检修计划已成功保存到: {output_path}")

    # 2. 根据 region_name 筛选机组并计算总功率降低
    margin_df = margin_df * 10  # margin_df 的单位是 万千瓦，乘以10转化为 MW
    plan_to_plot_df = maintenance_plan_df
    plot_label_suffix = "总检修功率"
    if region_name != '全省':
        if 'zone' not in maintenance_plan_df.columns:
            print(f"错误: maintenance_plan_df 中缺少 'zone' 列，无法按区域 '{region_name}' 筛选。")
            return
        plan_to_plot_df = maintenance_plan_df[maintenance_plan_df['zone'] == region_name]
        plot_label_suffix = f"{region_name} 检修功率"

    maint_arr = np.zeros(8760)
    for _, row in plan_to_plot_df.iterrows():
        start_idx = int(row['maintenance_start_hour_idx'])
        end_idx = int(row['maintenance_end_hour_idx'])
        power_mw = row["max_p_mw"]
        # 切片 end_idx+1 是为了包含结束的那一小时
        maint_arr[start_idx:end_idx + 1] += power_mw

    # 3. 生成图表
    if region_name not in margin_df.columns:
        print(f"错误: 在 margin_df 的列中找不到区域 '{region_name}'。无法生成图表。")
        print(f"margin_df 中的可用区域: {list(margin_df.columns)}")
        return

    print(f"正在为区域生成图表: {region_name}...")
    plt.figure(figsize=(16, 8))
    plt.plot(maint_arr, label=plot_label_suffix)
    plt.plot(margin_df[region_name].values, label=f"{region_name} 系统裕度", linestyle='--')
    plt.title(f"{region_name} 检修计划与系统裕度")
    plt.xlabel("年度小时数")
    plt.ylabel("功率 (MW)")
    plt.grid(True)
    plt.legend()
    plt.tight_layout()
    plt.show()


if __name__ == "__main__":
    import os

    # base_dir = os.path.dirname(os.path.abspath(__file__))
    # _gen_df = pd.read_excel(
    #     os.path.join(base_dir, "2025时序_煤电无检修.20250521_103033.xlsx"), sheet_name='gen', index_col=0
    # )
    # _margin_df = pd.read_excel(os.path.join(base_dir, "indicator.xlsx"), index_col=None)
    # _zone_map = pd.read_json(os.path.join(base_dir, "gen_area.json"), orient='index').rename(columns={0: "分区名"})
    base_dir = "/users/zhangrunfeng/Downloads"
    _gen_df = pd.read_excel(os.path.join(base_dir, "gen.xlsx"), index_col=0)
    _margin_df = pd.read_excel(os.path.join(base_dir, "new_indicator.xlsx"), index_col=0)
    _zone_map = pd.read_excel(os.path.join(base_dir, "gen_area.xlsx"), index_col='index')
    res = run_maintenance_plan(gen_df=_gen_df, margin_df=_margin_df, zone_df=_zone_map)
    save_and_plot_maintenance_results(
        maintenance_plan_df=res, margin_df=_margin_df, output_dir=base_dir, region_name="通西北"
    )
