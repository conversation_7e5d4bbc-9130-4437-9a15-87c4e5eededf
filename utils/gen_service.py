import os
import zipfile

import openpyxl
import pandas as pd
from loguru import logger

import common
import utils
from alg.common_utils import data_utils
from utils.gen_maintenance import run_maintenance_plan


def gen_maintenance(case_id: str, is_maintenance: bool = False):
    # 读取算例信息
    logger.info("开始读取算例数据")
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id)
    else:
        read_resp = utils.read_case(case_id=case_id, is_cache=False)
        case = read_resp.data
    logger.info("读取算例数据完成")
    case_file_name = case.get("filename", "")  # 算例文件名称
    full_case_filename = f"{case_id}_{case_file_name}"
    # 供电裕度
    output = case.get('output', {})
    indicator_data = case.get("indicator", {}).get("indicator_data", {})

    network = case.get("network", {})
    case_info = case.get("case_info", {})
    area_details = case_info.get('zone').get('device_relay')  # 机组分区信息
    gen_status = 2
    gen_error = ""
    try:
        if is_maintenance:
            # gens为检修不合理的机组， 数据类型为dataframe
            gens, new_indicator, gen_area = data_utils.gen_process2(
                output=output,
                indicator_data=indicator_data,
                network=network,
                area_details=area_details,
                is_maintenance=is_maintenance
            )
        else:
            # 安排检修
            gen, new_indicator, gen_area = data_utils.gen_process2(
                output=output,
                indicator_data=indicator_data,
                network=network,
                area_details=area_details,
                is_maintenance=is_maintenance
            )
            gens = gen.loc[(gen['type'] == "coal") & (~gen['maintenance_day'].isna())]
        if gens.empty:
            # 表示没有待机组检修优化或者安排检修的机组
            # 1: 未检修，2: 检修中，3: 已检修，4:没有需要检修的机组，5:检修失败
            gen_status = 4
            return
        gen_area = gen_area.set_index("index")
        gen_result = run_maintenance_plan(gen_df=gens, margin_df=new_indicator, zone_df=gen_area)
        # 获取检修曲线的长度
        maintenance_len = len(network.timeseries.columns[2:])

        def write_back_maintenance(row):
            # 回写检修曲线数据到算例文件
            index = row.name  # 机组行索引
            if is_maintenance:
                suffix = "机组优化"
            else:
                suffix = "机组检修"
            prefix_maintenance = [f"{row['name']}_{suffix}", "maintenance"]
            # 初始化检修曲线，默认都是0
            maintenance_list = [0] * maintenance_len
            result_row = gen_result.loc[index, ['maintenance_start_hour_idx', 'maintenance_end_hour_idx']].values
            maintenance_start_hour_idx = result_row[0]
            maintenance_end_hour_idx = result_row[1]
            maintenance_list[maintenance_start_hour_idx:maintenance_end_hour_idx +
                             1] = [1] * (maintenance_end_hour_idx - maintenance_start_hour_idx + 1)
            if is_maintenance:
                # 机组检修优化
                gen_timeseries = row['timeseries'].values[0]
                if isinstance(gen_timeseries, str):
                    idx = gen_timeseries.split(",")
                    for ix in idx:
                        ix = int(ix)
                        type_value = network.timeseries.loc[ix, 'type']
                        if not pd.notna(type_value) or type_value != "maintenance":
                            continue
                        prefix_maintenance = [network.timeseries.loc[ix, 'name'], 'maintenance']
                        network.timeseries.loc[ix] = prefix_maintenance + maintenance_list
            else:
                # 对timeseries表，追加一行检修记录
                new_add_row_index = len(network.timeseries)
                network.timeseries.loc[new_add_row_index] = prefix_maintenance + maintenance_list
                old_value = network.gen.loc[index, "timeseries"]
                if pd.notna(old_value):
                    new_value = f"{old_value},{new_add_row_index}"
                else:
                    new_value = new_add_row_index
                network.gen.loc[index, "timeseries"] = new_value

        gens.apply(write_back_maintenance, axis=1)
        # 拷贝原始算例文件
        origin_case_file = os.path.join(common.file_storage, full_case_filename)
        t_file = os.path.join(common.gen_maintenance_path, f"{full_case_filename.removesuffix('.teap')}.xlsx")
        if os.path.isfile(t_file):
            # 检修之后的文件若是存在，则删除
            os.remove(t_file)
        with zipfile.ZipFile(origin_case_file, 'r') as zip_ref:
            for name in zip_ref.namelist():
                if not name.endswith(".xlsx"):
                    continue
                # 解压单个文件
                zip_ref.extract(name, common.gen_maintenance_path)
                s_file = os.path.join(common.gen_maintenance_path, name)
                os.rename(s_file, t_file)
                break
        logger.info("解压算例结果文件中的原始算例完成")
        wb = openpyxl.load_workbook(t_file, read_only=True)
        worksheets = [v.title for v in wb.worksheets]
        wb.close()
        df_dict = pd.read_excel(t_file, sheet_name=worksheets)
        # 写入 Excel 文件
        with pd.ExcelWriter(t_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
            # 遍历电网的所有属性（如 bus, line, load 等）
            for key in network.keys():
                if key in ['gen', 'timeseries'] and isinstance(network[key], pd.DataFrame):
                    # 将组件数据写入以组件名为 Sheet 名的工作表
                    network[key].to_excel(writer, sheet_name=key, index=True)
                elif key in df_dict:
                    df_dict[key].to_excel(writer, sheet_name=key, index=True)

        logger.info(f"机组检修已完成")
        gen_status = 3
    except Exception as e:
        logger.exception(e)
        gen_status = 5
        gen_error = str(e)
    finally:
        db_data = utils.get_db_data()
        case = db_data.get(case_id, {})
        case['gen_status'] = gen_status
        case['gen_error'] = gen_error
        utils.save_db_data(db_data)
