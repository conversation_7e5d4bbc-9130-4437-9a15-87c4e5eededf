import json
import os
from collections import defaultdict
from typing import Dict

import common


def get_projects():
    """
    获取工程列表
    """
    db_data = defaultdict(dict)
    if not os.path.isfile(common.project_production_db_path):
        return db_data
    with open(common.project_production_db_path, 'r', encoding="utf8") as fp:
        content = fp.read()
        if content:
            db_data = json.loads(content)
    return db_data


def save_project(db_data: Dict):
    """
    保存工程
    """
    with open(common.project_production_db_path, 'w', encoding="utf8") as fp:
        fp.write(json.dumps(db_data, ensure_ascii=False, indent=4))
    return db_data
