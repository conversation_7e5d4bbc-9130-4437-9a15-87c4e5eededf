import os
import shutil
import time
import zipfile

import numpy as np
import pandas as pd

import common


class CaseStatistc:
    """
    算例数据统计
    """
    def __init__(self, case_id: str, teap_file: str):
        self.case_id = case_id
        self.teap_file = teap_file
        self.case_file_name = os.path.basename(self.teap_file)
        if not os.path.isfile(self.teap_file):
            raise Exception(f"算例文件不存在: {self.teap_file}")
        self.tp = int(time.time())
        self.work = os.path.join(common.case_statistic_path, case_id, str(self.tp))
        if os.path.isdir(self.work):
            shutil.rmtree(self.work)
        os.makedirs(self.work, exist_ok=True)  # 创建工作目录
        self.result_dir = ""  # 结果文件目录
        self.case_file = ""  # 原始算例文件
        self.decompression()  # 解压文件

    def decompression(self):
        """
        解压文件
        """
        with zipfile.ZipFile(self.teap_file, "r") as zip_ref:
            zip_ref.extractall(self.work)
        for file in os.listdir(self.work):
            if file.startswith("result_"):
                file_path = os.path.join(self.work, file)
                result_file_name = os.path.splitext(file_path)[0]
                extract_dir = os.path.join(self.work, result_file_name)
                os.makedirs(extract_dir, exist_ok=True)
                self.result_dir = extract_dir
                with zipfile.ZipFile(file_path, "r") as zip_fp:
                    zip_fp.extractall(extract_dir)
            if file.endswith(".xlsx"):
                self.case_file = os.path.join(self.work, file)

    def statistic(self):
        """
        统计数据
        """
        data = {
            "storage": self.storage,  # 储能运行小时数（小时）
            "day_power_shortage": self.day_power_shortage,  # 日最大缺电时长（小时）
            "power_shortage": self.power_shortage,  # 电力缺额
            "system_operating_cost": self.system_operating_cost,  # 系统运行成本
            "power_shortage_days": self.power_shortage_days,  # 存在电力缺额天数
        }
        # 删除临时工作目录
        shutil.rmtree(self.work, ignore_errors=True)
        return data

    @property
    def storage(self):
        """
        储能运行小时数
        """
        df1 = pd.read_excel(self.case_file, sheet_name="stogen", index_col=0)
        df2 = pd.read_csv(os.path.join(self.result_dir, "stogen_output.csv"), encoding='GBK', index_col=0)
        df2 = df2.iloc[:, 2:]
        return round(float(df2[df2 > 0].sum().sum() / df1['max_p_discharge_mw'].sum()), 4)

    @property
    def day_power_shortage(self):
        """
        日缺电时长(小时)
        """
        df = pd.read_csv(os.path.join(self.result_dir, "load_curtailment.csv"), encoding='GBK', index_col=0)
        df = df.iloc[:, 2:]
        df = df.sum(axis=0)
        df = pd.DataFrame(df, columns=["value"])
        df['value'] = np.where(df['value'] > 0, 1, 0)
        reshaped_data = df['value'].values.reshape(365, 24)
        df_daily = pd.DataFrame(reshaped_data, columns=[f'hour_{i}' for i in range(24)])
        df_daily = df_daily.sum(axis=1)
        return float(df_daily.max())

    @property
    def power_shortage(self):
        """
        电力缺额
        """
        df = pd.read_csv(os.path.join(self.result_dir, "load_curtailment.csv"), encoding='GBK', index_col=0)
        df = df.iloc[:, 2:]
        return float(df.values.sum() / 10)

    @property
    def system_operating_cost(self):
        """
        系统运行成本
        """
        df = pd.read_csv(os.path.join(self.result_dir, "gen_power_cost.csv"), encoding='GBK', index_col=0)
        df = df.iloc[:, 2:]
        return round(float(df.values.sum() / 100000000), 4)

    @property
    def power_shortage_days(self):
        """
        存在电力缺额天数
        """
        df = pd.read_csv(os.path.join(self.result_dir, "load_curtailment.csv"), encoding='GBK', index_col=0)
        df = df.iloc[:, 2:]
        df = df.sum(axis=0)
        df = pd.DataFrame(df, columns=["value"])
        df['value'] = np.where(df['value'] > 0, 1, 0)
        reshaped_data = df['value'].values.reshape(365, 24)
        df_daily = pd.DataFrame(reshaped_data, columns=[f'hour_{i}' for i in range(24)])
        df_daily = df_daily.sum(axis=1)
        return int(np.where(df_daily > 0, 1, 0).sum())
