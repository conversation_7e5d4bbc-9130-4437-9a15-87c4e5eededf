import pandas as pd

import common
from data import params
from tsa.workflow import tsa as tsa_flow
from tsa.workflow import tsc_opf as tsc

map_inference_result = {"N-1故障失稳数": "N1", "N-2故障失稳数": "N2", "功角失稳数": "tsi", "电压失稳数": "tvsi", "暂稳失稳总数": "count"}

map_qr_code = {"功角失稳": "tsi", "电压失稳": "tvsi", "功角&电压失稳": "both"}


def stable_inference(
    item: params.StableInferenceParams, case_start_time: str, case_file: str, mask_fault: bool = False
):
    # 暂稳推理/评估/预测, 保供主要用
    common.tas_analyzer_mark_false = tsa_flow.Analyzer(
        sample_id="js2025_v250307",
        sim_start_time=case_start_time,
        start_time=item.stime,
        end_time=item.etime,
        targets=['tsi', 'tvsi'],
        tsi_thresh=0.01,
        tvsi_thresh=0.9,
        device='cpu',
        fault_set_id='所有N-1和N-2故障集',
        mask_fault=False,
        case_dir=case_file
    )
    common.tas_analyzer_mark_true = tsa_flow.Analyzer(
        sample_id="js2025_v250307",
        sim_start_time=case_start_time,
        start_time=item.stime,
        end_time=item.etime,
        targets=['tsi', 'tvsi'],
        tsi_thresh=0.01,
        tvsi_thresh=0.9,
        device='cpu',
        fault_set_id='所有N-1和N-2故障集',
        mask_fault=True,
        case_dir=case_file
    )
    ai_dict = {"tsi": item.models, "tvsi": item.models}
    # 详细结果文件
    common.ai_stability_mark_true, common.bpa_stability_mark_true, common.bpa_dict_mark_true = common.tas_analyzer_mark_true.bpa_ai_compare(
        ai_dict=ai_dict
    )
    common.ai_stability_mark_false, common.bpa_stability_mark_false, common.bpa_dict_mark_false = common.tas_analyzer_mark_false.bpa_ai_compare(
        ai_dict=ai_dict
    )
    if mask_fault:
        analyzer = common.tas_analyzer_mark_true
        bpa_dict = common.bpa_dict_mark_true
    else:
        analyzer = common.tas_analyzer_mark_false
        bpa_dict = common.bpa_dict_mark_false

    ai_base_data = bpa_dict['人工智能']
    scale_base_data = bpa_dict['仿真校核']
    ai_data = {
        "unstable_number": ai_base_data['失稳故障数'],
        "total_fault": ai_base_data['总故障数'],
        "tvsi_fault": ai_base_data['电压失稳故障数'],
        "tsi_fault": ai_base_data['功角失稳故障数'],
    }
    scale_data = {
        "unstable_number": scale_base_data['失稳故障数'],
        "total_fault": scale_base_data['总故障数'],
        "tvsi_fault": scale_base_data['电压失稳故障数'],
        "tsi_fault": scale_base_data['功角失稳故障数'],
    }
    ai_qr_code = ai_base_data['全年稳定风险概览图']
    scale_qr_code = scale_base_data['全年稳定风险概览图']
    year = case_start_time.split("-")[0]
    ai_tsi_list = []
    ai_tvsi_list = []
    ai_both_list = []
    for k, dates in ai_qr_code.items():
        if k == "功角失稳":
            ai_tsi_list = dates
        elif k == "电压失稳":
            ai_tvsi_list = dates
        elif k == "功角&电压失稳":
            ai_both_list = dates
    scale_tsi_list = []
    scale_tvsi_list = []
    scale_both_list = []
    for k, dates in scale_qr_code.items():
        if k == "功角失稳":
            scale_tsi_list = dates
        elif k == "电压失稳":
            scale_tvsi_list = dates
        elif k == "功角&电压失稳":
            scale_both_list = dates
    ai_qr_code_list = []
    scale_qr_code_list = []
    # 生成从当年1月1日到当年12月31日的日期序列
    date_range = pd.date_range(start=f'{year}-01-01', end=f'{year}-12-31', freq='D').astype(str).tolist()
    for month in range(1, 13):
        ai_month_list = []
        scale_month_list = []
        for day in range(1, 32):
            date_value = f"{year}-{month:02}-{day:02}"
            if date_value not in date_range:
                ai_value = -1
            elif date_value in ai_tsi_list:
                ai_value = 1
            elif date_value in ai_tvsi_list:
                ai_value = 2
            elif date_value in ai_both_list:
                ai_value = 3
            else:
                ai_value = 0
            ai_month_list.append({"value": ai_value})
            if date_value not in date_range:
                scale_value = -1
            elif date_value in scale_tsi_list:
                scale_value = 1
            elif date_value in scale_tvsi_list:
                scale_value = 2
            elif date_value in scale_both_list:
                scale_value = 3
            else:
                scale_value = 0
            scale_month_list.append({"value": scale_value})
        ai_qr_code_list.append(ai_month_list)
        scale_qr_code_list.append(scale_month_list)
    # 获取地图数据
    map_df = analyzer.geo_diagram()
    map_line_data = []
    map_point_data = []
    map_data = map_df.to_dict("list")
    map_point_names = []
    for i in range(len(map_df)):
        # line_data
        line_value_list = [
            map_data['N-1功角失稳次数'][i],
            map_data['N-1电压失稳次数'][i],
            map_data['N-2功角失稳次数'][i],
            map_data['N-2电压失稳次数'][i],
        ]
        line_dict = {
            "dispname_from": map_data['dispname_from'][i],
            "dispname_to": map_data['dispname_to'][i],
            "from_bus": map_data['from_bus'][i],
            "to_bus": map_data['to_bus'][i],
            "value": int(sum(line_value_list)),
            "valueList": line_value_list,
            "coords":
                [
                    [
                        map_data['lon_from'][i],
                        map_data['lat_from'][i],
                    ], [
                        map_data['lon_to'][i],
                        map_data['lat_to'][i],
                    ]
                ]
        }
        map_line_data.append(line_dict)
        # point_data
        if map_data['dispname_from'][i] not in map_point_names:
            point_dict = {
                "name": map_data['dispname_from'][i],
                "value": [map_data['lon_from'][i], map_data['lat_from'][i], 0]
            }
            map_point_data.append(point_dict)
            map_point_names.append(map_data['dispname_from'][i])
        if map_data['dispname_to'][i] not in map_point_names:
            point_dict = {
                "name": map_data['dispname_to'][i],
                "value": [map_data['lon_to'][i], map_data['lat_to'][i], 0]
            }
            map_point_data.append(point_dict)
            map_point_names.append(map_data['dispname_to'][i])
    ai_data['qr_code_list'] = ai_qr_code_list
    ai_data['qr_code'] = ai_qr_code
    scale_data['qr_code_list'] = scale_qr_code_list
    scale_data['qr_code'] = scale_qr_code

    result_dict = {
        "ai_data": ai_data,
        "scale_data": scale_data,
        "map": {
            "point_data": map_point_data,
            "line_data": map_line_data
        }
    }
    return result_dict


def get_stable_day_analyze(time_index: int):
    data_dict = {}
    for v in ["tsi", "tvsi"]:
        controller = tsc.BGController(
            sample_id='js_2024_base', ts_index=time_index, ai_id='js_2024_base', target=v
        )
        resp = controller.print_result()
        data_dict[v] = resp
    return data_dict
