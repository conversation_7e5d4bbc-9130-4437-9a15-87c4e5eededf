import datetime as dt
import json
import os
from collections import defaultdict
from decimal import Decimal
from typing import Dict
from typing import Optional

import h5py
import numpy as np
import pandapower as pp
from loguru import logger

import common
from alg.common_utils import data_utils
from data import Response
from utils import gen_service


def response_obj() -> Response:
    return Response()


def get_current_datetime() -> str:
    """
    获取当前时间
    :return:
    """
    nowtime = str(dt.datetime.now()).split('.')[0]
    return nowtime


def get_db_data(case_id: Optional[str] = None, is_short: int = 1) -> Dict:
    """
    获取单个算例信息
    :param case_id: 算例id
    :param is_short: 算例id, 1: 长期算例表, 2: 中短期，仅限河南存在
    """
    db_data = defaultdict(dict)
    if is_short == 1:
        db_file = common.db_path
    else:
        db_file = common.db_short_file
    if not os.path.isfile(db_file):
        return db_data
    with open(db_file, 'r', encoding="utf-8") as fp:
        content = fp.read()
        if content:
            db_data = json.loads(content)
    if case_id:
        case = db_data.get(case_id, {})
        return case
    return db_data


def get_token() -> Dict:
    """
    获取token登陆信息
    """
    login_data = {}
    if not os.path.isfile(common.db_login):
        return login_data
    with open(common.db_login, 'r') as fp:
        txt = fp.read()
        if txt:
            login_data = json.loads(txt)
    return login_data


def save_token(data: Dict):
    """
    保存token登陆信息
    """
    with open(common.db_login, 'w') as fp:
        fp.write(json.dumps(data, indent=4, ensure_ascii=False))
    return


def clear_case_files(case_id: str, filename: str):
    """
    删除成功时需要清除之前存储的算例文件，机组检修的结果文件
    """
    # 删除成功时需要清除之前存储的算例文件
    file_path = os.path.join(common.file_storage, f"{case_id}_{filename}")
    if os.path.isfile(file_path):
        os.remove(file_path)
    network_file = os.path.join(common.db_network_dir, f"{case_id}.json")
    if os.path.isfile(network_file):
        os.remove(network_file)
    output_file = os.path.join(common.db_output_dir, f"{case_id}.h5")
    if os.path.isfile(output_file):
        os.remove(output_file)
    cal_result_file = os.path.join(common.cal_result_path, f"{case_id}.json")
    if os.path.isfile(cal_result_file):
        os.remove(cal_result_file)
    case_info_file = os.path.join(common.case_info_path, f"{case_id}.json")
    if os.path.isfile(case_info_file):
        os.remove(case_info_file)
    indicator_file = os.path.join(common.indicator_path, f"{case_id}.json")
    if os.path.isfile(indicator_file):
        os.remove(indicator_file)
    ptdf_file = os.path.join(common.db_ptdf_dir, f"{case_id}.h5")
    if os.path.isfile(ptdf_file):
        os.remove(ptdf_file)
    # 删除检修结果文件
    gen_file = os.path.join(common.gen_maintenance_path, f"{case_id}_{filename.removesuffix('.teap')}.xlsx")
    if os.path.isfile(gen_file):
        os.remove(gen_file)


def save_db_data(db_data: Dict, is_short: int = 1):
    """
    保存算例数据
    """
    if is_short == 1:
        db_file = common.db_path
    else:
        db_file = common.db_short_file
    with open(db_file, 'w', encoding="utf-8") as fp:
        fp.write(json.dumps(db_data, indent=4, ensure_ascii=False))
    return


def calculate_size(v: int) -> str:
    """
    计算单位
    :param value:
    :return:
    """
    value = v
    if v > 1024:
        value = value / 1024
        v = v // 1024
    else:
        return f"{value}B"
    if v > 1024:
        value = value / 1024
        v = v // 1024
    else:
        return f"{round(value, 2)}KB"
    if v > 1024:
        value = value / 1024
        v = v // 1024
    else:
        return f"{round(value, 2)}MB"
    return f"{round(value, 2)}GB"


class JsonEncoder(json.JSONEncoder):
    """Convert numpy classes to JSON serializable objects."""
    def default(self, obj):
        if isinstance(obj, (np.integer, np.floating, np.bool_)):
            return obj.item()
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, float):
            return Decimal(str(obj))
        else:
            return super(JsonEncoder, self).default(obj)


def callback_alg_save_data(
    case_id: str, data: Dict, status: int = 2, log_file: Optional[str] = None, is_short: int = 1
):
    """
    算法分析统计数据回调函数,主要用于保存算法统计出来的数据到mongodb
    :param case_id: 算例id
    :param data: 算例数据， base: 基础数据, series: 时序数据, indicator: 指标数据
    :param status:算例分析状态,0: 未分析,1:分析中,2:已分析,3: 分析失败
    :param result: 返回值
    :return
    """
    data['status'] = status
    # 获取锁
    common.lock.acquire()
    db_data = get_db_data(is_short=is_short)
    case = db_data.get(case_id)
    if not case:
        logger.error(f"回调函数中未查询到算例数据")
        return
    try:
        if status == 3:
            case['status'] = 3
            db_data[case_id] = case
            save_db_data(db_data, is_short=is_short)
            return
        case_info = {}
        cal_result = {}
        if 'network' in data:
            network = data.pop("network")
            filename = os.path.join(common.db_network_dir, f"{case_id}.json")
            pp.to_json(network, filename)
        if 'result_output' in data:
            result_output = data.pop('result_output', {})
            filename = os.path.join(common.db_output_dir, f"{case_id}.h5")
            with h5py.File(filename, 'w') as fp:
                for k, v in result_output.items():
                    fp.create_dataset(k, data=v, compression="gzip", compression_opts=9)
        if 'ptdf' in data:
            ptdf = data.pop('ptdf')
            ptdf_filename = os.path.join(common.db_ptdf_dir, f"{case_id}.h5")
            with h5py.File(ptdf_filename, 'w') as fp:
                fp.create_dataset('ptdf', data=ptdf, compression="gzip", compression_opts=9)
        if 'cal_result' in data:
            cal_result = data.pop('cal_result')
            cal_result_value = json.dumps(cal_result, indent=4, ensure_ascii=False, cls=JsonEncoder)
            cal_result_filename = os.path.join(common.cal_result_path, f"{case_id}.json")
            with open(cal_result_filename, 'w') as fp:
                fp.write(cal_result_value)
        if 'case_info' in data:
            case_info = data.pop('case_info')
            case_info_value = json.dumps(case_info, indent=4, ensure_ascii=False, cls=JsonEncoder)
            case_info_filename = os.path.join(common.case_info_path, f"{case_id}.json")
            with open(case_info_filename, 'w') as fp:
                fp.write(case_info_value)
        if 'indicator' in data:
            indicator = data.pop('indicator')
            indicator_value = json.dumps(indicator, indent=4, ensure_ascii=False, cls=JsonEncoder)
            indicator_filename = os.path.join(common.indicator_path, f"{case_id}.json")
            with open(indicator_filename, 'w') as fp:
                fp.write(indicator_value)
        for k, v in data.items():
            if k == 'status':
                case['status'] = v
            else:
                case[k] = json.dumps(v, ensure_ascii=False, cls=JsonEncoder)
        if status == 2:
            # 获取时序安排值
            time_range = case_info.get("time_range", [])
            case['time_value'] = len(time_range)
            # 获取最高负荷值
            extreme_data = data_utils.get_grid_extreme_value_info(
                case_info_dict=case_info, cal_result_dict=cal_result
            )
            max_load_p_value = extreme_data.get("max_load_p", [])
            if max_load_p_value:
                case['max_load'] = max_load_p_value[0]
            else:
                case['max_load'] = 0
            # 获取集中式新能源装机
            allgen_data = data_utils.get_allgen_capacity(case_info_dict=case_info)
            new_energy_zhuangji = 0
            for obj in allgen_data:
                if obj['name'] in ['光伏', '风电']:
                    new_energy_zhuangji += obj['value']
            case['new_energy_zhuangji'] = round(new_energy_zhuangji, 2)
        # 保存数据
        db_data[case_id] = case
        save_db_data(db_data, is_short=is_short)
    except Exception as e:
        if log_file:
            logger.add(log_file)
        case['status'] = 3
        db_data[case_id] = case
        save_db_data(db_data, is_short=is_short)
        logger.exception(e)
    finally:
        # 释放锁
        common.lock.release()
        logger.info(f"分析算例结束")
    return


def read_case(
    case_id: str,
    is_network: bool = True,
    is_output: bool = True,
    is_short: int = 1,
    is_cache: bool = True
) -> Response:
    """
    读取算例信息，并添加缓存
    :param case_id: 算例id
    :param is_network: 是否读取网络数据
    :param is_output: 是否读取结果集
    :param is_short: 1: 长期算例, 2: 中短期算例，仅限河南
    :param is_cache: True：读取算例，添加缓存，False：不添加到缓存，仅是读取算例信息
    :return:
    """
    result = Response()
    if is_short == 1:
        db_file = common.db_path
    else:
        db_file = common.db_short_file
    if not os.path.isfile(db_file):
        result.code = 1010
        result.msg = '暂无数据'
        return result
    # 获取锁
    common.lock.acquire()
    try:
        db_data = get_db_data(is_short=is_short)
        case_obj = db_data.get(case_id)
        if not case_obj:
            result.code = 1001
            result.msg = '未查询到算例信息'
            return result
        # 添加缓存并返回算例信息
        if is_network:
            network_file = os.path.join(common.db_network_dir, f"{case_id}.json")
            network = pp.from_json(network_file)
            case_obj['network'] = network
        if is_output:
            filename = os.path.join(common.db_output_dir, f"{case_id}.h5")
            with h5py.File(filename, 'r') as h5_file:
                output = {}
                for key in h5_file.keys():
                    v = h5_file[key]
                    output[key] = np.array(v)
                case_obj['output'] = output
        # 读取ptdf
        ptdf_filename = os.path.join(common.db_ptdf_dir, f"{case_id}.h5")
        if os.path.isfile(ptdf_filename):
            with h5py.File(ptdf_filename, 'r') as ptdf_file:
                case_obj['ptdf'] = ptdf_file['ptdf'][:]
        # 读取cal_result
        cal_result_filename = os.path.join(common.cal_result_path, f"{case_id}.json")
        if os.path.isfile(cal_result_filename):
            with open(cal_result_filename, 'r') as cal_result_file:
                cal_result_content = cal_result_file.read()
                case_obj['cal_result'] = json.loads(cal_result_content)
        # 读取case_info
        case_info_filename = os.path.join(common.case_info_path, f"{case_id}.json")
        if os.path.isfile(case_info_filename):
            with open(case_info_filename, 'r') as case_info_file:
                case_info_content = case_info_file.read()
                case_obj['case_info'] = json.loads(case_info_content)
        # indicator
        indicator_filename = os.path.join(common.indicator_path, f"{case_id}.json")
        if os.path.isfile(indicator_filename):
            with open(indicator_filename, 'r') as indicator_file:
                indicator_content = indicator_file.read()
                case_obj['indicator'] = json.loads(indicator_content)
        result.data = case_obj
        if not is_cache:
            # 不添加到内存缓存，直接返回算例信息
            return result
        # 获取系统设置的缓存算例的数量
        if not os.path.isfile(common.db_system_path):
            cache_number = 1
        else:
            with open(common.db_system_path, 'r') as fp:
                content = fp.read()
                if not content:
                    cache_number = 1
                else:
                    contnet = json.loads(content)
                    cache_number = contnet.get('cache_number', 1)
        # 内存缓存中仅缓存三个算例信息，超过三个，则剔除一个，再把当前算例加入缓存
        # 短期算例缓存，特殊处理，和正常算例缓存不在一起
        if is_short == 2:
            common.short_case_dict = {case_id: case_obj}
            logger.info(f'中短期算例case_id[{case_id}] 添加缓存成功')
        else:
            if case_id in common.case_dict:
                common.case_dict[case_id] = case_obj
            else:
                if len(common.case_dict) >= cache_number:
                    key = list(common.case_dict.keys())[0]
                    common.case_dict.pop(key)
                common.case_dict[case_id] = case_obj
            logger.info(f'case_id[{case_id}] 添加缓存成功')
    finally:
        # 释放锁
        common.lock.release()
    return result


def valid_token(token: str) -> Response:
    """
    校验token是否有效
    """
    login_data = get_token()
    if token not in login_data:
        return Response(code=1001, msg="尚未登陆")
    login_info = login_data.get(token, {})
    etime = login_info.get("etime")
    if not etime:
        return Response(code=1001, msg="登陆已过期")
    new_etime = dt.datetime.strptime(etime, "%Y-%m-%d %H:%M:%S")
    nowtime = dt.datetime.now()
    if nowtime > new_etime:
        return Response(code=1001, msg="登陆已过期")
    return Response(data=login_info)


def clear_invalid_token():
    """
    清除已过期的token信息
    """
    login_data = get_token()
    nowtime = dt.datetime.now()
    for token in list(login_data.keys()):
        item = login_data.get(token)
        if not isinstance(item, dict):
            continue
        etime = item.get("etime")
        if not etime:
            del login_data[token]
            continue
        new_etime = dt.datetime.strptime(etime, "%Y-%m-%d %H:%M:%S")
        if nowtime > new_etime:
            del login_data[token]
    save_token(login_data)


def calculate_hour_indexes(date: dt.datetime):
    # 计算这一天是一年中的第几天
    day_of_year = date.timetuple().tm_yday

    # 计算这一天之前的总小时数
    total_hours_before = (day_of_year - 1) * 24

    # 计算这一天的24个小时在一年中的索引号
    hour_indexes = [total_hours_before + hour for hour in range(24)]

    return hour_indexes


def get_year_hours(date_str: str):
    """获取指定日期所在年份的总小时数"""
    date = dt.datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
    year = date.year
    # 判断是否为闰年
    is_leap = (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0)
    total_days = 366 if is_leap else 365
    return total_days * 24


def get_hour_index(date_str, base_time: Optional[dt.datetime] = None):
    """计算指定日期在其年份中的小时索引位置"""
    date = dt.datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
    if not base_time:
        base_time = dt.datetime(date.year, 1, 1)
    # 计算从年初到指定日期的时间差（秒）
    delta = date - base_time
    # 转换为小时索引（整数）
    return int(delta.total_seconds() // 3600)


def check_gen_maintenance():
    """
    启动时检查是否存在正在检修中的算例，若是存在，则重新检修直至完成
    """
    logger.info("检查是否存在检修中的算例")
    count = 0
    db_data = get_db_data()
    for case_id, case in db_data.items():
        gen_status = case.get("gen_status")
        gen_type = case.get("gen_type")
        if gen_status != 2:
            continue
        if not gen_type:
            # 当前算例处于检修中，但是没有检修类型，则把状态改为未检修
            case['gen_status'] = 1
            continue
        args = {"is_maintenance": gen_type, "case_id": case_id}
        # 提交线程池，执行机组检修
        common.pool.submit(gen_service.gen_maintenance, **args)
        count += 1
    logger.info(f"检修中的算例数量：{count}")


def bus_name_vnkv(name: str, vn_kv: float):
    vn_kv = str(vn_kv).rstrip("0").rstrip(".")
    str_name = f"{name}{vn_kv}"
    return str_name
