import json
import os
from typing import Optional
from typing import Union

import pandas as pd
from fastapi import APIRouter
from fastapi import Depends

import common
import data
import utils
from alg.common_utils import data_utils
from data import params

router = APIRouter()


@router.get("/case/all", tags=[common.HUNAN_TAG], response_model=data.Response)
def get_all_case(result: data.Response = Depends(utils.response_obj)):
    """
    获取所有算例
    :param result: 返回值
    :return:
    """
    if not os.path.isfile(common.db_path):
        result.data = []
        return result
    # 读取h5数据集
    db_data = utils.get_db_data()
    case_list = []
    for case_id, case in db_data.items():
        if case.get('status') != 4:
            continue
        new_case = {
            'id': case_id,
            'name': case.get('name', ''),
            'filename': case.get("filename", ''),
            'year': case.get('year', 0),
            'status': case.get('status', 0),  # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
            'create_time': case.get('create_time'),
            'update_time': case.get('update_time'),
            'size': utils.calculate_size(case.get("size", 0)),
            'content_type': case.get("content_type", ''),
            'comment': case.get("comment", '')
        }
        case_list.append(new_case)
    result.data = case_list
    return result


# 结果时序数据
@router.post("/time/sequence", tags=[common.HUNAN_TAG], response_model=data.Response)
def time_sequence(item: params.getSequenceTimeParams, result: data.Response = Depends(utils.response_obj)):
    """
    获取时序数据
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    cal_result = case.get('cal_result', {})
    times = []
    if item.time_list:
        data = data_utils.get_power_balance_data_curve(
            cal_result_dict=cal_result,
            time_list=item.time_list,
            is_all=False,
            area_details_dict=case_info['zone']['device_relay'],
        )
        time_range = case_info.get("time_range", [])
        if time_range:
            for i in item.time_list:
                times.append(time_range[i])
    else:
        data = data_utils.get_power_balance_data_curve(
            cal_result_dict=cal_result,
            area_details_dict=case_info['zone']['device_relay'],
        )
        times = case_info.get("time_range", [])
    result.data = {'value': data, 'time': times}
    return result


# 装机容量
@router.get("/zhuangji/capacity", tags=[common.HUNAN_TAG], response_model=data.Response)
def zhuangji_capacity(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取装机容量
    :param result:
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    data = data_utils.get_allgen_capacity(case_info_dict=case_info)
    result.data = data
    return result


# 发电量
@router.get("/electric/capacity", tags=[common.HUNAN_TAG], response_model=data.Response)
def energy_xiaona_rate(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取发电量
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    cal_result = case.get('cal_result', {})
    config = case_info.get("config", {})
    data = data_utils.get_allgen_electric_hours(cal_result_dict=cal_result, freq_h='season', config=config)
    electricity = data.get("electricity", {})
    if not electricity:
        return result
    total = 0
    for v in electricity.values():
        total += v
    electricity['total'] = round(total, 2)
    result.data = electricity
    return result


@router.get("/annual/typical/time", tags=[common.HUNAN_TAG], response_model=data.Response)
def annual_typical_time(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取全年典型方式时刻/特殊工况时刻列表
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    indicator = case.get('indicator', {})
    data = data_utils.get_typical_run_mod(indicator_dict=indicator)
    # 处理典型方式
    typical_mod = data.get("typical_mod", {})
    new_mod = {}
    for k, v in typical_mod.items():
        values = []
        for i in range(12):
            if i == 0:
                new_v = v + i
                values.append(new_v)
            else:
                new_v1 = v - i
                new_v2 = v + i
                values.append(new_v1)
                values.append(new_v2)
        values.sort()
        new_mod[k] = values
    data['typical_mod'] = new_mod
    result.data = data
    return result


@router.get("/annual/extreme/data", tags=[common.HUNAN_TAG], response_model=data.Response)
def annual_extreme_data(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    全年极值信息，示例：最大负荷, 最大峰谷差, 最大供电缺口...
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    cal_result = case.get('cal_result', {})
    data = data_utils.get_grid_extreme_value_info(case_info_dict=case_info, cal_result_dict=cal_result)
    # 时间序列
    time_range = case_info.get("time_range", [])
    new_data = {}
    for k, value_list in data.items():
        if isinstance(value_list[1], int):
            new_data[k] = {"value": value_list[0], "time": time_range[value_list[1]], "time_index": value_list[1]}
        else:
            new_data[k] = {"value": value_list[0], "unit": value_list[1], "time_index": value_list[1]}
    result.data = new_data
    return result


@router.get("/energy/xiaona/general", tags=[common.HUNAN_TAG], response_model=data.Response)
def energy_xiaona_general(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取可再生能源消纳总览/指标
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    cal_result = case.get("cal_result", {})
    case_info = case.get('case_info', {})
    config = case_info.get("config", {})
    data = data_utils.get_allgen_electric_hours(cal_result_dict=cal_result, freq_h='season', config=config)
    result.data = data['consump_rate']
    return result


@router.get("/electric/abandon/data", tags=[common.HUNAN_TAG], response_model=data.Response)
def electric_abandon_data(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取弃电分布(年弃电+日弃电)
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    cal_result = case.get("cal_result", {})
    data = data_utils.get_electric_distribute(cal_result_dict=cal_result)
    result.data = data
    return result


@router.get("/abandon/electricity/record", tags=[common.HUNAN_TAG], response_model=data.Response)
def abandon_electricity_record(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取弃电记录
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    cal_result = case.get("cal_result", {})
    case_info = case.get("case_info", {})
    config = case_info.get("config")
    data = data_utils.get_newenergy_curtailment_data(config=config, cal_result_dict=cal_result)
    times = case_info.get("time_range", [])
    result.data = {'value': data, 'time': times}
    return result


@router.get("/electric/supply/type", tags=[common.HUNAN_TAG], response_model=data.Response)
def electric_supply_type(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取供电形式分布特征
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    cal_result = case.get("cal_result", {})
    data = data_utils.get_power_supply_distribute(cal_result_dict=cal_result)
    result.data = data
    return result


@router.get("/electric/supply/review", tags=[common.HUNAN_TAG], response_model=data.Response)
def electric_supply_review(
    case_id: str, time_no: Optional[Union[str, int]] = None, result: data.Response = Depends(utils.response_obj)
):
    """
    获取供电能力评价
    :param case_id: 算例id
    :param time_no: 时刻
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    indicator = case.get('indicator', {})
    if time_no and isinstance(time_no, str) and time_no.isdigit():
        time_no = int(time_no)
    data = data_utils.get_allzone_indicators(indicator_rlt_dict=indicator, time_no=time_no)
    result.data = data
    return result


@router.get("/device/load/data", tags=[common.HUNAN_TAG], response_model=data.Response)
def device_load_data(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取设备负载分析
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    cal_result = case.get("cal_result", {})
    data = data_utils.get_all_device_loadratio_distribute(cal_result_dict=cal_result, casee_address='hunan')
    result.data = data
    return result


@router.post("/device/laodratio/timestep", tags=[common.HUNAN_TAG], response_model=data.Response)
def device_laodratio_timestep(
    item: params.DeviceInterfaceParams, result: data.Response = Depends(utils.response_obj)
):
    """
    获取指定时刻的断面分析
    :param item: 参数
    :param result: 返回值
    :return:
    """
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get("output", {})
    network = case.get('network')
    data = data_utils.get_inf_power_timesteps(net=network, result_dict=output, time_no=item.time_list)
    case_info = case.get("case_info", {})
    time_range = case_info.get("time_range", [])
    result.data = {"time": time_range, "interface": data}
    return result


@router.get("/channel/load/data", tags=[common.HUNAN_TAG], response_model=data.Response)
def channel_load_data(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取关键通道负载分析数据
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get("output", {})
    network = case.get('network')
    data = data_utils.get_inf_power_timesteps(net=network, result_dict=output, inf_max=True)
    channel_data = {}
    for k, v in data.items():
        if k not in ['南电北送', '西电东送']:
            continue
        if k == '南电北送':
            v['power'] = v['power'][6]
            v['p_rate'] = v['p_rate'][6]
            for obj in v.get('detail', []):
                obj['power'] = obj['power'][6]
                obj['p_rate'] = obj['p_rate'][6]
        if k == '西电东送':
            v['power'] = v['power'][2]
            v['p_rate'] = v['p_rate'][2]
            for obj in v.get('detail', []):
                obj['power'] = obj['power'][2]
                obj['p_rate'] = obj['p_rate'][2]
        channel_data[k] = v
    result.data = channel_data
    return result


@router.get("/case/years", tags=[common.HUNAN_TAG], response_model=data.Response)
def case_years(result: data.Response = Depends(utils.response_obj)):
    """
    获取算例所有年份
    :param result: 返回值
    :return:
    """
    if not os.path.isfile(common.db_path):
        result.code = 1010
        result.msg = '暂无数据'
        return result
    df = pd.read_hdf(path_or_buf=common.db_path, key=common.db_key)
    df_status = df.loc[df['status'] == 4, ['id', 'year']]
    if len(df_status) == 0:
        result.code = 1011
        result.msg = '暂无已完成分析的算例数据'
        return result
    years = []
    for obj in df_status.values:
        years.append({"year": obj[1], 'case_id': obj[0]})
    result.data = years
    common.case_year_dict = years
    return result


@router.get("/map/network/data", tags=[common.HUNAN_TAG], response_model=data.Response)
def map_network_data(
    case_id: str,
    time_no: Optional[Union[str, int]] = None,
    area: str = "全省",
    result: data.Response = Depends(utils.response_obj)
):
    """
    电网拓扑地图及分区
    :param result:
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get("output", {})
    case_info = case.get('case_info', {})
    if time_no and isinstance(time_no, str) and time_no.isdigit():
        time_no = int(time_no)
        data = data_utils.get_network_topo(
            case_info_dict=case_info, time_no=time_no, result_dict=output, area_name=area
        )
    else:
        cal_result = case.get('cal_result', {})
        extreme_data = data_utils.get_grid_extreme_value_info(
            case_info_dict=case_info, cal_result_dict=cal_result
        )
        max_load_p_list = extreme_data.get("max_load_p", [])
        if max_load_p_list:
            time_no = max_load_p_list[1]
            data = data_utils.get_network_topo(
                case_info_dict=case_info, result_dict=output, area_name=area, time_no=time_no
            )
    result.data = data
    return result
