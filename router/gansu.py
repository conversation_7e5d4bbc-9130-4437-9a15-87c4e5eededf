import json
import os
from dataclasses import asdict
from typing import Optional
from typing import Union

import pandas as pd
from fastapi import APIRouter
from fastapi import Depends

import common
import data
import utils
from alg.common_utils import balance_data_utils
from alg.common_utils import calculator_view
from alg.common_utils import data_utils
from data import params

router = APIRouter()


@router.get('/case/all', tags=[common.GANSU_TAG], response_model=data.Response)
def all_case(result: data.Response = Depends(utils.response_obj)):
    """
    获取所有算例
    :param result: 返回值
    :return:
    """
    if not os.path.isfile(common.db_path):
        result.data = []
        return result
    # 读取h5数据集
    db_data = utils.get_db_data()
    case_list = []
    for case_id, case in db_data.items():
        if case.get('status') != 4:
            continue
        # 读取case_info
        case_info = {}
        case_info_filename = os.path.join(common.case_info_path, f"{case['id']}.json")
        if os.path.isfile(case_info_filename):
            with open(case_info_filename, 'r') as case_info_file:
                case_info_content = case_info_file.read()
                case_info = json.loads(case_info_content)
        time_range = case_info.get("time_range", [])
        new_case = {
            'id': case_id,
            'name': case.get('name', ''),
            'filename': case.get("filename", ''),
            'year': case.get('year', 0),
            'status': case.get('status', 0),  # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
            'create_time': case.get('create_time'),
            'update_time': case.get('update_time'),
            'size': utils.calculate_size(case.get("size", 0)),
            'content_type': case.get("content_type", ''),
            'comment': case.get("comment", ''),
            'time_value': case.get("time_value", 0),
            'max_load': case.get("max_load", 0),
            'new_energy_zhuangji': case.get("new_energy_zhuangji", 0),
            'time_range': time_range
        }
        case_list.append(new_case)
    result.data = case_list
    return result


# 装机容量
@router.get("/zhuangji/capacity", tags=[common.GANSU_TAG], response_model=data.Response)
def zhuangji_capacity(case_id: str, area: str = "全省", result: data.Response = Depends(utils.response_obj)):
    """
    获取装机容量
    :param result:
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    data = data_utils.get_allgen_capacity(case_info_dict=case_info, area_name=area)
    result.data = data
    return result


# 结果时序数据
@router.get("/time/sequence", tags=[common.GANSU_TAG], response_model=data.Response)
def time_sequence(case_id: str, area: str = "全省", result: data.Response = Depends(utils.response_obj)):
    """
    获取时序数据
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    cal_result = case.get("cal_result", {})
    case_info = case.get('case_info', {})
    if area == '全省':
        data = data_utils.get_power_balance_data_curve(cal_result_dict=cal_result, area_name=area)
    else:
        output = case.get("output", {})
        config = case_info.get("config", {})
        network = case.get("network")
        data = data_utils.get_zone_balance_data_curve(
            net=network,
            config=config,
            result_dict=output,
            area_name=area,
            area_details_dict=case_info['zone']['device_relay'],
        )
    times = case_info.get("time_range", [])
    result.data = {"value": data, "time": times}
    return result


@router.get("/map/network/data", tags=[common.GANSU_TAG], response_model=data.Response)
def map_network_data(
    case_id: str,
    time_no: Optional[int] = None,
    period: str = 'week',
    pf_type: str = "mean",
    result: data.Response = Depends(utils.response_obj)
):
    """
    电网拓扑地图及分区
    :param case_id: 算例id
    :param period: 统计分析周期， week, month
    :param pf_type: 设备负载率类型: 'mean' or 'max'
    :param result:
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    cal_result = case.get('cal_result', {})
    output = case.get("output", {})
    if time_no:
        data = data_utils.get_network_topo_v2(
            case_info_dict=case_info,
            cal_rlt_topo_rate=cal_result.get("topo_dev_rate", {}),
            time_no=time_no,
            result_dict=output,
            period=period,
            pf_type=pf_type
        )
    else:
        data = data_utils.get_network_topo_v2(
            case_info_dict=case_info,
            cal_rlt_topo_rate=cal_result.get("topo_dev_rate", {}),
            time_no=None,
            result_dict=output,
            period=period,
            pf_type=pf_type
        )
    result.data = data
    return result


@router.get("/annual/extreme/data", tags=[common.GANSU_TAG], response_model=data.Response)
def annual_extreme_data(case_id: str, area: str = "全省", result: data.Response = Depends(utils.response_obj)):
    """
    全年极值信息，示例：最大负荷, 最大峰谷差, 最大供电缺口...
    :param result:
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    cal_result = case.get('cal_result', {})
    data = data_utils.get_grid_extreme_value_info(case_info_dict=case_info, cal_result_dict=cal_result)
    # 时间序列
    time_range = case_info.get("time_range", [])
    new_data = {}
    for k, value_list in data.items():
        if isinstance(value_list[1], int):
            new_data[k] = {"value": value_list[0], "time": time_range[value_list[1]]}
        else:
            new_data[k] = {"value": value_list[0], "unit": value_list[1]}
    result.data = new_data
    return result


@router.get("/device/reload/list", tags=[common.GANSU_TAG], response_model=data.Response)
def device_reload_list(case_id: str, area: str = "全省", result: data.Response = Depends(utils.response_obj)):
    """
    重载风险设备列表
    :param result:
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    cal_result = case.get('cal_result', {})
    case_info = case.get('case_info', {})
    data = data_utils.get_all_device_loadratio_info(
        cal_result_dict=cal_result, area_name=area, area_details=case_info['zone']['device_relay']
    )
    trafo = data.get("trafo", {})  # 关键主变负载分析
    new_data = {}
    trafo_len = len(trafo.get("index", []))
    keys = list(trafo.keys())
    trafo_list = []
    for i in range(trafo_len):
        new_dict = {}
        for k in keys:
            new_dict[k] = trafo[k][i]
        trafo_list.append(new_dict)
    new_data['trafo'] = trafo_list
    line = data.get("line", {})  # 关键线路负载分析
    line_len = len(line.get("index", []))
    keys = list(line.keys())
    line_list = []
    for i in range(line_len):
        new_dict = {}
        for k in keys:
            new_dict[k] = line[k][i]
        line_list.append(new_dict)
    new_data['line'] = line_list
    interface = data.get("interface", {})  # 关键线路负载分析
    interface_len = len(interface.get("index", []))
    keys = list(interface.keys())
    interface_list = []
    for i in range(interface_len):
        new_dict = {}
        for k in keys:
            new_dict[k] = interface[k][i]
        interface_list.append(new_dict)
    new_data['interface'] = interface_list
    result.data = new_data
    return result


@router.get("/energy/xiaona/rate", tags=[common.GANSU_TAG], response_model=data.Response)
def energy_xiaona_rate(case_id: str, area: str = "全省", result: data.Response = Depends(utils.response_obj)):
    """
    获取能源消纳率数据
    :param result:
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    cal_result = case.get('cal_result', {})
    config = case_info.get("config", {})
    data = data_utils.get_allgen_electric_hours(
        cal_result_dict=cal_result, area_name=area, freq_h='season', config=config
    )
    result.data = data
    return result


@router.get("/power/balance/data", tags=[common.GANSU_TAG], response_model=data.Response)
def power_balance_data(
    case_id: str, time_no: int, area: str = "全省", result: data.Response = Depends(utils.response_obj)
):
    """
    获取电力平衡数据最大值和最小值
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    boundary_data = case.get('indicator', {}).get('boundary_data', {})
    data = data_utils.get_simulation_power_boundry(
        boundary_original=boundary_data, area_name=area, timestep=time_no
    )
    result.data = data
    return result


@router.get("/annual/typical/time", tags=[common.GANSU_TAG], response_model=data.Response)
def annual_typical_time(case_id: str, area: str = "全省", result: data.Response = Depends(utils.response_obj)):
    """
    获取全年典型方式时刻/特殊工况时刻列表
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    indicator = case.get('indicator', {})
    data = data_utils.get_typical_run_mod(indicator_dict=indicator)
    result.data = data
    return result


@router.get("/zone/all/indicator", tags=[common.GANSU_TAG], response_model=data.Response)
def zone_all_indicator(
    case_id: str,
    time_no: Union[str, int],
    area: str = "全省",
    result: data.Response = Depends(utils.response_obj)
):
    """
    获取全网的分区所有指标
    :param case_id: 算例id
    :param time_no: 时刻
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    if isinstance(time_no, str) and time_no.isdigit():
        time_no = int(time_no)
    indicator = case.get('indicator', {})
    case_info = case.get("case_info", {})
    output = case.get("output", {})
    data = data_utils.get_allzone_indicators(
        indicator_rlt_dict=indicator,
        time_no=time_no,
        area_name=area,
        area_details=case_info['zone']['device_relay'],
        result_dict=output
    )
    result.data = data
    return result


@router.get("/device/laodratio/timestep", tags=[common.GANSU_TAG], response_model=data.Response)
def device_laodratio_timestep(
    case_id: str, time_no: int, area: str = "全省", result: data.Response = Depends(utils.response_obj)
):
    """
    获取指定时刻的所有设备的有功、负载率、限额数据, 主变/线路/断面
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get('output', {})
    network = case.get("network")
    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    data = data_utils.get_device_loadratio_timestep(
        net=network,
        result_dict=output,
        time_no=time_no,
        config=config,
        area_name=area,
        area_detail=case_info['zone']['device_relay']
    )
    result.data = data
    return result


@router.post("/deduce/power/data", tags=[common.GANSU_TAG], response_model=data.Response)
def deduce_power_data(item: params.DeducePowerParams, result: data.Response = Depends(utils.response_obj)):
    """
    推演接口
    :param item: 参数
    :param result: 返回值
    :return:
    """
    user_input_data = asdict(item)
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    # result_output
    output = case.get('output', {})
    network = case.get("network")
    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    indicator = case.get("indicator", {})
    area_details = case_info['zone']['device_relay']
    data = calculator_view.run_calculator_func(
        user_input_data=user_input_data,
        timestep=item.timestep,
        case_result_dict=output,
        case_net=network,
        config=config,
        indicator_rlt_dict=indicator,
        area_details=area_details
    )
    result.data = data
    return result


@router.get("/case/years", tags=[common.GANSU_TAG], response_model=data.Response)
def case_years(result: data.Response = Depends(utils.response_obj)):
    """
    获取算例所有年份
    :param result: 返回值
    :return:
    """
    if not os.path.isfile(common.db_path):
        result.code = 1010
        result.msg = '暂无数据'
        return result
    df = pd.read_hdf(path_or_buf=common.db_path, key=common.db_key)
    df_status = df.loc[df['status'] == 4, ['id', 'year']]
    if len(df_status) == 0:
        result.code = 1011
        result.msg = '暂无已完成分析的算例数据'
        return result
    years = []
    for obj in df_status.values:
        years.append({"year": obj[1], 'case_id': obj[0]})
    result.data = years
    common.case_year_dict = years
    return result


@router.get("/zone/load/sufficiencey", tags=[common.GANSU_TAG], response_model=data.Response)
def zone_load_sufficiencey(
    case_id: str,
    bload_series: bool = False,
    bzone_detail: bool = False,
    bdetail: bool = False,
    area: str = "全省",
    result: data.Response = Depends(utils.response_obj)
):
    """
    获取所有分区的负荷最大缺口及总缺负荷时长;缺负荷明细;
    :param case_id: 算例id
    :param bload_series: 是否需要负荷时序
    :param bzone_detail: 全网时是否需要分区的负荷削减情况
    :param bdetail: 是否需要负荷削减的具体明细
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get('output', {})
    case_info = case.get("case_info", {})
    data = data_utils.get_zone_loadinsufficiency_data(
        result_dict=output,
        bload_series=bload_series,
        bzone_detail=bzone_detail,
        bdetail=bdetail,
        area_name=area,
        area_details_dict=case_info['zone']['device_relay']
    )
    result.data = data
    return result


@router.get("/zone/trafo/capability", tags=[common.GANSU_TAG], response_model=data.Response)
def zone_trafo_capability(
    case_id: str,
    time_no: Union[str, int],
    area: str = "全省",
    result: data.Response = Depends(utils.response_obj)
):
    """
    分区推演时用于获取分区主变受电能力和以变电站为单位的主变负载率初值
    :param case_id: 算例id
    :param time_no: 时刻
    :param area: 区域
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get('output', {})
    network = case.get("network")
    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    if isinstance(time_no, str) and time_no.isdigit():
        time_no = int(time_no)
    ptdf = case.get("ptdf")
    data = data_utils.get_zone_trafo_capability_timestep(
        net=network,
        config=config,
        result_dict=output,
        timestep=time_no,
        net_ptdf=ptdf,
        area_name=area,
        area_details=case_info['zone']['device_relay']
    )
    result.data = data
    return result


@router.get("/zone/all/psm", tags=[common.GANSU_TAG], response_model=data.Response)
def zone_all_psm(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取全省/所有分区的最小供电裕度/平均供电裕度, 有供电缺口时长/供电紧张时长
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get('output', {})
    indicator = case.get("indicator", {})
    case_info = case.get("case_info", {})
    cal_result = case.get("cal_result", {})
    data = data_utils.get_allzone_psm_value(
        indicator_rlt_dict=indicator,
        result_dict=output,
        area_details=case_info['zone']['device_relay'],
        ana_result=cal_result['consump_rate']['zone'],
        ana_result_new_curtail_dict=cal_result['cutailment_hours']['zone']
    )
    result.data = data
    return result


@router.get("/zone/newenergy/consumpt", tags=[common.GANSU_TAG], response_model=data.Response)
def zone_newenergy_consumpt(
    case_id: str, period: str = 'week', result: data.Response = Depends(utils.response_obj)
):
    """
    计算分区/全网新能源消纳率的日、周、月分布;
    :param case_id: 算例id
    :param period: 日、周、月
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get('output', {})
    case_info = case.get("case_info", {})
    data = balance_data_utils.get_zone_newenergy_consumpt_rate_distribute(
        case_info_dict=case_info,
        result_dict=output,
        area_details=case_info['zone']['device_relay'],
        period=period
    )
    result.data = data
    return result


@router.get("/device/load/data", tags=[common.GANSU_TAG], response_model=data.Response)
def device_load_data(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取设备负载分析
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    cal_result = case.get("cal_result", {})
    data = data_utils.get_all_device_loadratio_distribute(cal_result_dict=cal_result, casee_address='gansu')
    result.data = data
    return result


@router.get("/abandon/electricity/record", tags=[common.GANSU_TAG], response_model=data.Response)
def abandon_electricity_record(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取弃电记录
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    cal_result = case.get("cal_result", {})
    case_info = case.get("case_info", {})
    config = case_info.get("config")
    data = data_utils.get_newenergy_curtailment_data(config=config, cal_result_dict=cal_result)
    result.data = data
    return result


@router.get("/electric/abandon/data", tags=[common.GANSU_TAG], response_model=data.Response)
def electric_abandon_data(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取弃电分布(年弃电+日弃电)
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    cal_result = case.get("cal_result", {})
    data = data_utils.get_electric_distribute(cal_result_dict=cal_result)
    result.data = data
    return result
