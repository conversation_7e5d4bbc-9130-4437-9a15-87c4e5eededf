import datetime as dt
import json
import os
import shutil
import time
import zipfile
from collections import defaultdict
from dataclasses import asdict
from typing import Dict
from typing import Optional
from typing import Union
from urllib.parse import quote_plus

import numpy as np
import pandas as pd
import requests
from fastapi import APIRouter
from fastapi import Depends
from loguru import logger
from starlette.responses import FileResponse

import common
import data
import utils
from alg.common_utils import balance_data_utils
from alg.common_utils import calculator_view
from alg.common_utils import data_utils
from data import params
from utils import gen_service
from utils import project_service
from utils import tsa_service

router = APIRouter()


@router.get('/case/all', tags=[common.JIANGSU_TAG], response_model=data.Response)
def all_case(result: data.Response = Depends(utils.response_obj)):
    """
    获取所有算例
    :param result: 返回值
    :return:
    """
    if not os.path.isfile(common.db_path):
        result.data = []
        return result
    # 读取h5数据集
    db_data = utils.get_db_data()
    case_list = []
    for case_id, case in db_data.items():
        if case.get('status') != 4:
            continue
        if not case.get('show', True):
            continue
        # 读取case_info
        case_info = {}
        case_info_filename = os.path.join(common.case_info_path, f"{case['id']}.json")
        if os.path.isfile(case_info_filename):
            with open(case_info_filename, 'r') as case_info_file:
                case_info_content = case_info_file.read()
                case_info = json.loads(case_info_content)
        time_range = case_info.get("time_range", [])
        new_case = {
            'id': case_id,
            'name': case.get('name', ''),
            'filename': case.get("filename", ''),
            'year': case.get('year', 0),
            'status': case.get('status', 0),  # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
            'create_time': case.get('create_time'),
            'update_time': case.get('update_time'),
            'size': utils.calculate_size(case.get("size", 0)),
            'content_type': case.get("content_type", ''),
            'comment': case.get("comment", ''),
            'time_range': time_range,
            'time_value': case.get("time_value", 0),
            'max_load': case.get("max_load", 0),
            'new_energy_zhuangji': case.get("new_energy_zhuangji", 0)
        }
        case_list.append(new_case)
    result.data = case_list
    return result


@router.get("/zhuangji/capacity", tags=[common.JIYUAN_TAG], response_model=data.Response)
def zhuangji_capacity(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取装机容量
    :param result:
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    data = data_utils.get_allgen_capacity(case_info_dict=case_info)
    result.data = data
    return result


@router.get("/device/number", tags=[common.JIYUAN_TAG], response_model=data.Response)
def device_number(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取设备数目
    :param result:
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    data = data_utils.get_devices_number(case_info_dict=case_info)
    result.data = data
    return result


@router.get("/annual/extreme/data", tags=[common.JIANGSU_TAG], response_model=data.Response)
def annual_extreme_data(case_id: str, area: str = "全省", result: data.Response = Depends(utils.response_obj)):
    """
    全年极值信息，示例：最大负荷, 最大峰谷差, 最大供电缺口...
    :param result:
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    cal_result = case.get('cal_result', {})
    data = data_utils.get_grid_extreme_value_info(case_info_dict=case_info, cal_result_dict=cal_result)
    # 时间序列
    time_range = case_info.get("time_range", [])
    new_data = {}
    for k, value_list in data.items():
        if isinstance(value_list[1], int):
            new_data[k] = {"value": value_list[0], "time": time_range[value_list[1]]}
        else:
            new_data[k] = {"value": value_list[0], "unit": value_list[1]}
    result.data = new_data
    return result


@router.post("/map/network/data", tags=[common.JIANGSU_TAG], response_model=data.Response)
def map_network_data(item: params.MapNetworkDataParams, result: data.Response = Depends(utils.response_obj)):
    """
    电网拓扑地图及分区
    :param result:
    :return:
    """
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get("output", {})
    case_info = case.get('case_info', {})
    if item.time_no:
        data = data_utils.get_network_topo(
            case_info_dict=case_info, time_no=item.time_no, result_dict=output, area_name=item.area
        )
    else:
        if item.value_type:
            data = data_utils.get_network_topo(
                case_info_dict=case_info, valueType=item.value_type, result_dict=output, area_name=item.area
            )
            result.data = data
            return result
        else:
            data = data_utils.get_network_topo(case_info_dict=case_info, result_dict=output, area_name=item.area)
    result.data = data
    return result


@router.get("/zone/all/psm", tags=[common.JIANGSU_TAG], response_model=data.Response)
def zone_all_psm(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取全省/所有分区的最小供电裕度/平均供电裕度, 有供电缺口时长/供电紧张时长
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get('output', {})
    indicator = case.get("indicator", {})
    case_info = case.get("case_info", {})
    cal_result = case.get("cal_result", {})
    data = data_utils.get_allzone_psm_value(
        indicator_rlt_dict=indicator,
        result_dict=output,
        area_details=case_info['zone']['device_relay'],
        ana_result=cal_result['consump_rate']['zone']
    )
    result.data = data
    return result


@router.get("/balance/margin/data", tags=[common.JIANGSU_TAG], response_model=data.Response)
def balance_margin_data(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    全年供电缺口/调峰缺口信息的年-月-日,月-日-小时分布--单位:万千瓦;
    :params:
    case_id: case id
    :return: dict
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    config = case_info.get("config", {})
    indicator = case.get("indicator", {})
    network = case.get("network")
    output = case.get('output', {})
    data = data_utils.get_grid_margin_data(
        net=network, result_dict=output, config=config, indicator_rlt_dict=indicator
    )
    result.data = data
    return result


@router.get("/electric/supply/type", tags=[common.JIANGSU_TAG], response_model=data.Response)
def electric_supply_type(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取供电形式分布特征
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    cal_result = case.get("cal_result", {})
    data = data_utils.get_power_supply_distribute(cal_result_dict=cal_result)
    result.data = data
    return result


@router.get("/zone/gap/code", tags=[common.JIANGSU_TAG], response_model=data.Response)
def zone_gap_code(case_id: str, area: str = "全省", result: data.Response = Depends(utils.response_obj)):
    """
    获取分区供电缺口的二维码接口
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    indicator = case.get('indicator', {})
    data = balance_data_utils.get_zone_margin_qr_data(config=config, indicator_rlt_dict=indicator, area_name=area)
    result.data = data
    return result


@router.get("/electric/supply/demand", tags=[common.JIANGSU_TAG], response_model=data.Response)
def electric_supply_demand(
    case_id: str, dis_type: str = 'hour', result: data.Response = Depends(utils.response_obj)
):
    """
    获取区域/分区的周期内供电缺口小时的0-23点小时分布
    :param case_id: 算例id
    :param dis_type: 分布特性分析类型,'hour': 0-23小时分布; 'month'：1-12月分布;
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    indicator = case.get('indicator', {})
    data = balance_data_utils.get_region_powerslack_hours(
        config=config, indicator_rlt_dict=indicator, disType=dis_type
    )
    result.data = data
    return result


@router.get("/electric/zone/hour/gap", tags=[common.JIANGSU_TAG], response_model=data.Response)
def electric_zone_hour_gap(case_id: str, time_no: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取所有分区的某时段内的供电裕度曲线
    :param case_id: 算例id
    :param time_no: 某一天，示例: 2024-03-06
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get("case_info", {})
    indicator = case.get('indicator', {})
    time_range = case_info.get("time_range", [])
    index = time_range.index(time_no)
    time_list = [index]
    for i in range(1, 24):
        time_list.append(index + i)
    data = data_utils.get_zone_psm_timeseries_data(indicator_rlt_dict=indicator, time_no=time_list)
    result.data = data
    return result


@router.get("/device/reload/list", tags=[common.JIANGSU_TAG], response_model=data.Response)
def device_reload_list(
    case_id: str,
    is_short: Union[int, str] = 1,
    area: str = "全省",
    result: data.Response = Depends(utils.response_obj)
):
    """
    重载风险设备列表
    :param result:
    :return:
    """
    if isinstance(is_short, str):
        is_short = int(is_short)
    if is_short == 1:
        cache_dict = common.case_dict
    else:
        cache_dict = common.short_case_dict
    if case_id in cache_dict:
        case = cache_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id, is_short=is_short)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    cal_result = case.get('cal_result', {})
    case_info = case.get('case_info', {})
    network = case.get("network")
    data = data_utils.get_all_device_loadratio_info(
        cal_result_dict=cal_result,
        area_name=area,
        case_inf_collection_dict=case_info.get("inf_collection", {}),
        inf_ele_type="trafo",
        net=network,
        area_details_dict=case_info['zone']['device_relay']
    )
    trafo = data.get("trafo", {})  # 关键主变负载分析
    new_data = {}
    trafo_len = len(trafo.get("index", []))
    keys = list(trafo.keys())
    trafo_list = []
    for i in range(trafo_len):
        new_dict = {}
        for k in keys:
            new_dict[k] = trafo[k][i]
        trafo_list.append(new_dict)
    new_data['trafo'] = trafo_list
    line = data.get("line", {})  # 关键线路负载分析
    line_len = len(line.get("index", []))
    keys = list(line.keys())
    line_list = []
    for i in range(line_len):
        new_dict = {}
        for k in keys:
            new_dict[k] = line[k][i]
        line_list.append(new_dict)
    new_data['line'] = line_list
    interface = data.get("interface", {})  # 关键断面负载分析
    interface_len = len(interface.get("index", []))
    keys = list(interface.keys())
    interface_list = []
    for i in range(interface_len):
        new_dict = {}
        for k in keys:
            new_dict[k] = interface[k][i]
        interface_list.append(new_dict)
    new_data['interface'] = interface_list
    channel = data.get("channel", {})  # 关键通道
    new_data['channel'] = channel
    result.data = new_data
    return result


@router.get("/device/zone_inf/list", tags=[common.JIANGSU_TAG], response_model=data.Response)
def zone_device_relay_inf_list(
    case_id: str,
    is_short: Union[int, str] = 1,
    area: str = "全省",
    result: data.Response = Depends(utils.response_obj)
):
    """
    重载风险设备列表
    :param result:
    :return:
    """
    if isinstance(is_short, str):
        is_short = int(is_short)
    if is_short == 1:
        cache_dict = common.case_dict
    else:
        cache_dict = common.short_case_dict
    if case_id in cache_dict:
        case = cache_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id, is_short=is_short)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    network = case.get("network")
    output = case.get("output", {})
    data = data_utils.get_zone_device_relay_inf_info(
        net=network,
        result_dict=output,
        area_name=area,
        area_details_dict=case_info['zone']['device_relay'],
        inf_ele_type="trafo"
    )
    result.data = data
    return result


@router.get("/old/case/all", tags=[common.JIANGSU_TAG], response_model=data.Response)
def old_case_all(result: data.Response = Depends(utils.response_obj)):
    """
    转发老接口。获取所有算例id
    :param result: 返回值
    :return:
    """
    url = "http://192.168.50.37:82/api/get_all_case_id/"
    body_data = {"group_id": 0}
    resp = requests.post(url=url, json=body_data).json()
    result.data = resp.get("all_case")
    return result


@router.post("/old/case/api", tags=[common.JIANGSU_TAG], response_model=data.Response)
def old_case_api(item: Dict, result: data.Response = Depends(utils.response_obj)):
    """
    转发老接口api
    :param result: 返回值
    :return:
    """
    url = "http://192.168.50.37:82/api/"
    resp = requests.post(url=url, json=item).json()
    result.data = resp.get("func_result")
    return result


@router.get("/tmp/simu_boundary", tags=[common.JIANGSU_TAG], response_model=data.Response)
def get_sys_simulation_boundary(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取机组装机/设备数目等信息;
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result

    result.data = dict()
    case_info = case.get('case_info', {})
    config = case_info.get("config", {})
    network = case.get("network")
    output = case.get("output", {})
    result.data = data_utils.get_sys_case_boundary(network, config, case_info, output)

    return result


@router.get("/time/tide/map", tags=[common.JIANGSU_TAG], response_model=data.Response)
def get_time_tide_map(case_id: str, area: str = "全省", result: data.Response = Depends(utils.response_obj)):
    """
    获取时序潮流分析中间的地图数据
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result

    case_info = case.get('case_info', {})
    output = case.get("output", {})
    result.data = data_utils.get_network_topo_attri(case_info_dict=case_info, result_dict=output, area_name=area)
    return result


@router.get("/time/tide/trafo/line/analysis", tags=[common.JIANGSU_TAG], response_model=data.Response)
def get_time_tide_trafo_line_analysis(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取时序潮流主变和线路限额分析
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result

    case_info = case.get('case_info', {})
    output = case.get("output", {})
    result.data = data_utils.get_device_ratio_distribute(case_info_dict=case_info, result_dict=output)
    return result


@router.post("/time/tide/surface/info", tags=[common.JIANGSU_TAG], response_model=data.Response)
def get_time_tide_surface_info(
    item: params.GetTimeTideSurfaceInfo, result: data.Response = Depends(utils.response_obj)
):
    """
    获取时序潮流断面信息
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    config = case_info.get("config", {})
    network = case.get("network", {})
    output = case.get("output", {})
    result.data = data_utils.get_ele_power_rate_qr_(
        net=network,
        config=config,
        result_dict=output,
        ele_type=item.ele_type,
        ele_dict=item.ele_dict,
        rate_mk=item.rate_mk
    )
    return result


@router.post("/power/supply/gap/time", tags=[common.JIANGSU_TAG], response_model=data.Response)
def power_supply_gap_time(
    item: params.GetPowerSupplyGapTime, result: data.Response = Depends(utils.response_obj)
):
    """
    获取全省供电缺口时长
    :param item: 参数
    :param result: 返回值
    :return:
    """
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result

    case_info = case.get('case_info', {})
    config = case_info.get("config", {})
    output = case.get("output", {})
    result.data = data_utils.get_data_hours_distribute(
        config=config,
        result_dict=output,
        value_type=item.value_type,
    )
    return result


@router.get("/device/station/type", tags=[common.JIANGSU_TAG], response_model=data.Response)
def device_station_type(case_id: str, ele_type: str = "bus", result: data.Response = Depends(utils.response_obj)):
    """
    获取设备厂站类型
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result

    network = case.get("network")
    result.data = data_utils.get_station_location(
        net=network,
        ele_type=ele_type,
    )
    return result


@router.get("/storage/energy/index", tags=[common.JIANGSU_TAG], response_model=data.Response)
def storage_energy_index(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取储能关键指标, 直接根据TEAP计算结果获取全网极值相关信息
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result

    output = case.get("output", {})
    result.data = data_utils.get_grid_statistic_info(
        result_dict=output,
    )
    return result


@router.post("/storage/energy/use/situation", tags=[common.JIANGSU_TAG], response_model=data.Response)
def storage_energy_use_situation(
    item: params.GetStorageEnergyUseSituation, result: data.Response = Depends(utils.response_obj)
):
    """
    获取全省储能调用情况分析
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result

    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    network = case.get("network")
    output = case.get("output", {})
    indicator = case.get("indicator", {})
    time_range = case_info.get("time_range", [])
    day_data = {}
    for time_no in item.time_list:
        time_list = []
        index = time_range.index(time_no)
        time_list.append(index)
        for i in range(1, 24):
            time_list.append(index + i)
        new_data = data_utils.get_zone_balance_data_curve(
            net=network,
            config=config,
            result_dict=output,
            is_all=item.is_all,
            time_list=time_list,
            area_name=item.area,
            area_details_dict=case_info['zone']['device_relay'],
            zone_psm_dict=indicator['indicator_data']
        )
        day_data[time_no] = new_data
    result.data = day_data
    return result


@router.get("/pathway/crossing/rivers/delivery", tags=[common.JIANGSU_TAG], response_model=data.Response)
def pathway_crossing_rivers_delivery(
    case_id: str, channel_name: str, result: data.Response = Depends(utils.response_obj)
):
    """
    获取过江通道输送能力二维码数据
    :param case_id: 算例id
    :param channel_name: 输送通道名称
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result

    output = case.get("output", {})
    network = case.get("network")
    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    result.data = balance_data_utils.get_channel_ability_qr_data(
        net=network,
        config=config,
        result_dict=output,
        inf_collection=case_info['inf_collection'],
        channel_name=channel_name
    )
    return result


@router.get("/channel/ratio/time", tags=[common.JIANGSU_TAG], response_model=data.Response)
def channel_ratio_time(
    case_id: str, channel_name: str, freq_h: str = "season", result: data.Response = Depends(utils.response_obj)
):
    """
    获取过江通道小时利用率数据
    :param case_id: 算例id
    :param channel_name: 输送通道名称
    :param freq_h: "分布统计方法","hour": 0-23小时分布; "month":月度分布; "season":季度分布;
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result

    output = case.get("output", {})
    network = case.get("network")
    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    result.data = balance_data_utils.get_channel_ratio_time_distribute(
        net=network,
        config=config,
        result_dict=output,
        inf_collection=case_info['inf_collection'],
        channel_name=channel_name,
        freq_h=freq_h
    )
    return result


@router.post("/power/min/margin", tags=[common.JIANGSU_TAG], response_model=data.Response)
def power_min_margin(item: params.GetPowerMinMarginParams, result: data.Response = Depends(utils.response_obj)):
    """
    获取最低供电裕度，分区的
    :param item: 参数
    :param result: 返回值
    :return:
    """
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result

    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    indicator_data = case.get("indicator", {}).get("indicator_data", {})
    result.data = balance_data_utils.get_zone_psm_time_distribute(
        config=config, indicator_data=indicator_data, zone_name_list=item.zone_name_list, freq_h=item.freq_h
    )
    return result


@router.post("/power/dcline/series", tags=[common.JIANGSU_TAG], response_model=data.Response)
def power_dcline_series(
    item: params.GetPowerDclineSeriesParams, result: data.Response = Depends(utils.response_obj)
):
    """
    获取天的直流功率时序数据
    :param item: 参数
    :param result: 返回值
    :return:
    """
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get("case_info", {})
    time_range = case_info.get("time_range", [])
    time_list = []
    for time_no in item.time_list:
        index = time_range.index(time_no)
        time_list.append(index)
        for i in range(1, 24):
            time_list.append(index + i)
    network = case.get("network")
    output = case.get("output", {})
    result.data, _ = balance_data_utils.get_dcline_power_series(
        net=network, result_dict=output, dc_line_idx=item.dc_line_idx, is_all=item.is_all, time_list=time_list
    )
    return result


@router.get("/dcline/nature/index", tags=[common.JIANGSU_TAG], response_model=data.Response)
def dcline_nature_index(
    case_id: str, freq_h: str = "season", result: data.Response = Depends(utils.response_obj)
):
    """
    获取直流性能展示数据
    :param case_id: 算例id
    :param channel_name: 输送通道名称
    :param freq_h: "分布统计方法","hour": 0-23小时分布; "month":月度分布; "season":季度分布;
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result

    output = case.get("output", {})
    network = case.get("network")
    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    result.data = balance_data_utils.get_casesys_control_info(
        net=network, config=config, result_dict=output, inf_collection=case_info['inf_collection'], freq_h=freq_h
    )
    return result


@router.post("/zone/trafo/capability", tags=[common.JIANGSU_TAG], response_model=data.Response)
def zone_trafo_capability(
    item: params.ZoneTrafoCapabilityParams, result: data.Response = Depends(utils.response_obj)
):
    """
    分区推演时用于获取分区主变受电能力和以变电站为单位的主变负载率初值
    :param case_id: 算例id
    :param time_no: 时刻
    :param area: 区域
    :param result: 返回值
    :return:
    """
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get('output', {})
    network = case.get("network")
    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    ptdf = case.get("ptdf")
    data = data_utils.get_zone_trafo_capability_timestep(
        net=network,
        config=config,
        result_dict=output,
        timestep=item.time_no,
        net_ptdf=ptdf,
        area_name=item.area,
        area_details=case_info['zone']['device_relay']
    )
    result.data = data
    return result


@router.get("/power/balance/data", tags=[common.JIANGSU_TAG], response_model=data.Response)
def power_balance_data(
    case_id: str, time_no: int, area: str = "全省", result: data.Response = Depends(utils.response_obj)
):
    """
    获取电力平衡数据最大值和最小值
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    case_info = case.get("case_info", {})

    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    boundary_data = case.get('indicator', {}).get('boundary_data', {})
    data = data_utils.get_simulation_power_boundry(
        boundary_original=boundary_data,
        area_name=area,
        timestep=time_no,
        area_details_dict=case_info['zone']['device_relay'],
        net=case.get("network")
    )
    result.data = data
    return result


@router.post("/zone/all/indicator", tags=[common.JIANGSU_TAG], response_model=data.Response)
def zone_all_indicator(item: params.ZoneAllIndicatorParams, result: data.Response = Depends(utils.response_obj)):
    """
    获取全网的分区所有指标
    :param case_id: 算例id
    :param time_no: 时刻
    :param result: 返回值
    :return:
    """
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    indicator = case.get('indicator', {})
    case_info = case.get("case_info", {})
    output = case.get("output", {})
    data = data_utils.get_allzone_indicators(
        indicator_rlt_dict=indicator,
        time_no=item.time_no,
        area_name=item.area,
        area_details=case_info['zone']['device_relay'],
        result_dict=output
    )
    result.data = data
    return result


@router.get("/device/laodratio/timestep", tags=[common.JIANGSU_TAG], response_model=data.Response)
def device_laodratio_timestep(
    case_id: str, time_no: int, area: str = "全省", result: data.Response = Depends(utils.response_obj)
):
    """
    获取指定时刻的所有设备的有功、负载率、限额数据, 主变/线路/断面
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get('output', {})
    network = case.get("network")
    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    data = data_utils.get_device_loadratio_timestep(
        net=network,
        result_dict=output,
        time_no=time_no,
        config=config,
        area_name=area,
        area_detail=case_info['zone']['device_relay']
    )
    result.data = data
    return result


@router.post("/deduce/power/data", tags=[common.JIANGSU_TAG], response_model=data.Response)
def deduce_power_data(item: params.DeducePowerParams, result: data.Response = Depends(utils.response_obj)):
    """
    推演接口
    :param item: 参数
    :param result: 返回值
    :return:
    """
    user_input_data = asdict(item)
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    # result_output
    output = case.get('output', {})
    network = case.get("network")
    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    indicator = case.get("indicator", {})
    area_details = case_info['zone']['device_relay']
    data = calculator_view.run_calculator_func(
        user_input_data=user_input_data,
        timestep=item.timestep,
        case_result_dict=output,
        case_net=network,
        config=config,
        indicator_rlt_dict=indicator,
        area_details=area_details
    )
    result.data = data
    return result


@router.post("/channel/relation/info", tags=[common.JIANGSU_TAG], response_model=data.Response)
def channel_relation_info(
    item: params.ChannelRelationInfoParams, result: data.Response = Depends(utils.response_obj)
):
    """
    获取关联通道信息
    :param item: 参数
    :param result:
    :return:
    """
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get("output", {})
    case_info = case.get('case_info', {})
    config = case_info.get("config", {})
    network = case.get("network")
    result.data = data_utils.get_channel_relayinf_info_qr_(
        net=network, config=config, result_dict=output, ele_dict=item.ele_dict
    )
    return result


@router.post("/network/topo/all", tags=[common.JIANGSU_TAG], response_model=data.Response)
def get_network_topo_all(
    item: params.GetNetworkTopoAllParams, result: data.Response = Depends(utils.response_obj)
):
    """
    获取全网某时刻的所有节点的关联主变、线路的潮流;
    :param item: 参数
    :param result:
    :return:
    """
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get("output", {})
    network = case.get("network")
    result.data = data_utils.get_network_topo_all(case_net=network, result_dict=output, time_no=item.time_no)
    return result


@router.get("/fault/line/number", tags=[common.JIANGSU_TAG], response_model=data.Response)
def get_fault_line_number(key: str = "所有N-1和N-2故障集", result: data.Response = Depends(utils.response_obj)):
    """
    获取故障线路数量
    :param item: 参数
    :param result:
    :return:
    """
    line_list = []
    count = 0
    with open(common.fault_set_json, "r") as fp:
        txt = fp.read()
        content = json.loads(txt)
        fault_line = content.get(key, {})
        for name, lines in fault_line.items():
            line_len = len(lines)
            count += line_len
            line_list.append({"name": name, "number": line_len})
    result.data = {"count": count, "lines": line_list}
    return result


@router.get("/smart/model/types", tags=[common.JIANGSU_TAG], response_model=data.Response)
def get_smart_model_types(result: data.Response = Depends(utils.response_obj)):
    """
    获取智能模型列表
    :param item: 参数
    :param result:
    :return:
    """
    models = defaultdict(dict)
    if os.path.isdir(common.tsi_model_path):
        for file in os.listdir(common.tsi_model_path):
            if file not in ["LGB", "XGB"]:
                continue
            name = common.map_model_field.get(file)
            if not name:
                continue
            next_path = os.path.join(common.tsi_model_path, file)
            for next_file in os.listdir(next_path):
                next_file = next_file.split(".")[0]
                if "2024" in next_file or next_file != "js2025_v250307":
                    continue
                if name in models:
                    if next_file not in models[name]['children']:
                        models[name]['children'].append(next_file)
                else:
                    models[name] = {"key": file, "children": [next_file]}
    if os.path.isdir(common.tvsi_model_path):
        for file in os.listdir(common.tvsi_model_path):
            if file not in ["LGB", "XGB"]:
                continue
            name = common.map_model_field.get(file)
            if not name:
                continue
            next_path = os.path.join(common.tvsi_model_path, file)
            for next_file in os.listdir(next_path):
                next_file = next_file.split(".")[0]
                if "2024" in next_file or next_file != "js2025_v250307":
                    continue
                if name in models:
                    if next_file not in models[name]['children']:
                        models[name]['children'].append(next_file)
                else:
                    models[name] = {"key": file, "children": [next_file]}
    result.data = models
    return result


@router.get("/stable/fault/option", tags=[common.JIANGSU_TAG], response_model=data.Response)
def get_stable_fault_option(result: data.Response = Depends(utils.response_obj)):
    """
    获取智能稳定故障集列表
    :param item: 参数
    :param result:
    :return:
    """
    if not os.path.isfile(common.fault_set_json):
        result.data = []
        return result
    with open(common.fault_set_json, "r") as fp:
        txt = fp.read()
        if txt:
            fault_dict = json.loads(txt)
        else:
            fault_dict = {}
    result.data = list(fault_dict.keys())
    return result


@router.post("/stable/inference", tags=[common.JIANGSU_TAG], response_model=data.Response)
def stable_inference(item: params.StableInferenceParams, result: data.Response = Depends(utils.response_obj)):
    """
    暂稳推理
    :param item: 参数
    :param result:
    :return:
    """
    logger.info(f"暂稳推理参数: {item}")
    case = utils.get_db_data(case_id=item.case_id)
    if not case:
        result.code = 1002
        result.msg = "未查询到算例信息"
        return result
    # 算例设置的开始时间
    case_start_time = case.get("start_time", "")
    case_file_name = case.get('filename', "")
    file_name = f"{item.case_id}_{case_file_name}"
    now_file = os.path.join(common.file_storage, file_name)
    target_file = os.path.join(common.file_tmp_storage, file_name)
    shutil.copy2(now_file, common.file_tmp_storage)
    # 分离文件名和扩展名
    name, old_extension = os.path.splitext(target_file)
    # 构建新的文件名
    new_file_name = f"{name}.zip"
    os.rename(target_file, new_file_name)
    # 构建解压后存放文件的新文件夹名称，这里简单以去掉.tar后缀作为文件夹名，你可按需调整
    extract_folder_name = os.path.splitext(new_file_name)[0]
    # 创建新的文件夹用于存放解压后的文件
    os.makedirs(extract_folder_name, exist_ok=True)
    # 开始解压tar包
    try:
        with zipfile.ZipFile(new_file_name, 'r') as zip_ref:
            zip_ref.extractall(extract_folder_name)
        logger.info(f"{new_file_name} 已成功解压到 {extract_folder_name}")
    except Exception as e:
        logger.error(f"解压过程出现错误: {e}")
        raise
    new_case_file_name = os.path.splitext(case_file_name)[0].strip("result_")
    case_file = f"{os.path.join(extract_folder_name, new_case_file_name)}.xlsx"
    resp = tsa_service.stable_inference(
        item=item, case_start_time=case_start_time, case_file=case_file, mask_fault=item.mask_fault
    )
    # 删除解压的文件夹
    shutil.rmtree(extract_folder_name)
    # 删除zip文件
    os.remove(new_file_name)
    result.data = resp
    return result


@router.post("/stable/day/detail", tags=[common.JIANGSU_TAG], response_model=data.Response)
def stable_day_detail(item: params.StableDayDetailParams, result: data.Response = Depends(utils.response_obj)):
    """
    获取暂稳推理单日详情数据
    :param item: 参数
    :param result:
    :return:
    """
    # tvsi: 电压失稳， tsi: 功角失稳
    if not isinstance(common.tas_analyzer_mark_false, tsa_service.tsa_flow.Analyzer):
        result.code = 1002
        result.msg = "暂未查询"
        return result
    if item.mask_fault:
        tas_analyzer = common.tas_analyzer_mark_true
        if item.type == 1:
            resp = tas_analyzer.day_detail_analysis(date=item.date, df_res=common.ai_stability_mark_true)
        else:
            resp = tas_analyzer.day_detail_analysis(date=item.date, df_res=common.bpa_stability_mark_true)
    else:
        tas_analyzer = common.tas_analyzer_mark_false
        if item.type == 1:
            resp = tas_analyzer.day_detail_analysis(date=item.date, df_res=common.ai_stability_mark_false)
        else:
            resp = tas_analyzer.day_detail_analysis(date=item.date, df_res=common.bpa_stability_mark_false)

    date_range = pd.date_range(start=f'{item.date} 00:00:00', end=f'{item.date} 23:00:00', freq='H').tolist()
    # 处理失稳数量
    tvsi_unstable_number = resp.get("电压失稳", {}).get("失稳数量", {})
    tsi_unstable_number = resp.get("功角失稳", {}).get("失稳数量", {})
    tvsi_value_list = []
    tsi_value_list = []
    for t in date_range:
        if t in tsi_unstable_number:
            tsi_value_list.append(tsi_unstable_number[t])
        else:
            tsi_value_list.append(0)
        if t in tvsi_unstable_number:
            tvsi_value_list.append(tvsi_unstable_number[t])
        else:
            tvsi_value_list.append(0)
    resp['电压失稳']['失稳数量'] = tvsi_value_list
    resp['功角失稳']['失稳数量'] = tsi_value_list
    # 处理失稳详情
    tvsi_unstable_detail = resp.get("电压失稳", {}).get("失稳详情", [])
    tsi_unstable_detail = resp.get("功角失稳", {}).get("失稳详情", [])
    tvsi_unstable_detail_list = []
    tsi_unstable_detail_list = []
    for obj in tvsi_unstable_detail:
        tvsi_dict = {
            "key": str(obj['time']),
            "value": obj['line'],
            "from_bus": obj['from_bus'],
            "to_bus": obj['to_bus'],
            "type": obj['type'],
            "time_idx": obj['time_idx']
        }
        tvsi_unstable_detail_list.append(tvsi_dict)
    for obj in tsi_unstable_detail:
        tsi_dict = {
            "key": str(obj['time']),
            "value": obj['line'],
            "from_bus": obj['from_bus'],
            "to_bus": obj['to_bus'],
            "type": obj['type'],
            "time_idx": obj['time_idx']
        }
        tsi_unstable_detail_list.append(tsi_dict)
    resp['电压失稳']['失稳详情'] = tvsi_unstable_detail_list
    resp['功角失稳']['失稳详情'] = tsi_unstable_detail_list
    result.data = resp
    return result


@router.post("/stable/day/curve", tags=[common.JIANGSU_TAG], response_model=data.Response)
def stable_day_curve(item: params.StableDayCurveParams, result: data.Response = Depends(utils.response_obj)):
    """
    获取暂稳推理单日详情数据-电压失稳（失稳节点列表，电压曲线），功角失稳（机组出力曲线、功角失稳曲线）
    :param item: 参数
    :param result:
    :return:
    """
    # tvsi: 电压失稳， tsi: 功角失稳
    if not isinstance(common.tas_analyzer_mark_false, tsa_service.tsa_flow.Analyzer):
        result.code = 1002
        result.msg = "暂未查询"
        return result
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    if item.mask_fault:
        tas_analyzer = common.tas_analyzer_mark_true
    else:
        tas_analyzer = common.tas_analyzer_mark_false
    resp = tas_analyzer.fault_analysis(
        time_idx=item.time_index,
        from_bus=item.from_bus,
        to_bus=item.to_bus,
        fault_type=item.fault_type,
        target=item.target
    )
    data_dict = {}
    if item.target == "tsi":
        # 功角失稳
        output = case.get("output", {})
        network = case.get("network")
        net_gen = network['gen']
        # 功角失稳，需要获取机组出力曲线数据
        names = resp.get("功角失稳发电机", [])
        dynamo_list = []
        if item.date:
            new_date = dt.datetime.strptime(item.date, "%Y-%m-%d")
            hours_indexs = utils.calculate_hour_indexes(date=new_date)
        else:
            hours_indexs = []
        for name in names:
            net_filter = net_gen[net_gen['name'] == name]
            if len(net_filter) == 0:
                raise Exception("未找到该发电机")
            obj = net_filter.iloc[0]
            fullname = obj['fullname']
            net_filter = net_gen[net_gen['fullname'] == fullname]
            indexs = net_filter.index.tolist()
            if hours_indexs:
                values = output['gen_output'][indexs, :].sum(axis=0)[hours_indexs]
            else:
                values = output['gen_output'][indexs, :].sum(axis=0)
            dynamo_dict = {"name": fullname, "values": values.tolist()}
            dynamo_list.append(dynamo_dict)
        data_dict['nodes'] = dynamo_list
        data_dict['voltage_curve'] = resp.get("失稳功角曲线", [])
    else:
        # 电压失稳
        data_dict["nodes"] = resp.get("电压失稳母线", [])
        data_dict['voltage_curve'] = resp.get("失稳电压曲线", [])
        data_dict['stable_voltage'] = resp.get("暂态电压", [])
    result.data = data_dict
    return result


@router.post("/stable/day/line/detail", tags=[common.JIANGSU_TAG], response_model=data.Response)
def stable_day_detail_by_line(
    item: params.StableDetailByLineParams, result: data.Response = Depends(utils.response_obj)
):
    """
    通过线路获取失稳详情
    :param item: 参数
    :param result:
    :return:
    """
    # tvsi: 电压失稳， tsi: 功角失稳
    if not isinstance(common.tas_analyzer_mark_false, tsa_service.tsa_flow.Analyzer):
        result.code = 1002
        result.msg = "暂未查询到数据"
        return result
    if item.mask_fault:
        tas_analyzer = common.tas_analyzer_mark_true
        if item.type == 1:
            # 人工智能
            df_stable = tas_analyzer.stability_table(common.ai_stability_mark_true)
        else:
            # 仿真
            df_stable = tas_analyzer.stability_table(common.bpa_stability_mark_true)
    else:
        tas_analyzer = common.tas_analyzer_mark_false
        if item.type == 1:
            # 人工智能
            df_stable = tas_analyzer.stability_table(common.ai_stability_mark_false)
        else:
            # 仿真
            df_stable = tas_analyzer.stability_table(common.bpa_stability_mark_false)

    tvsi_list = []
    tsi_list = []
    df = df_stable[(df_stable['from_bus'] == item.from_bus) & (df_stable['to_bus'] == item.to_bus)]
    for _, row in df.iterrows():
        obj_dict = {
            "key": str(row['time']),
            "time_idx": row['time_idx'],
            'value': f"{row['line']} {row['type']}",
            'type': row['type'],
            'from_bus': row['from_bus'],
            'to_bus': row['to_bus']
        }
        if row['功角失稳']:
            tsi_list.append(obj_dict)
        if row['电压失稳']:
            tvsi_list.append(obj_dict)
    result.data = {"tvsi": tvsi_list, "tsi": tsi_list}
    return result


@router.post("/stable/day/statistics", tags=[common.JIANGSU_TAG], response_model=data.Response)
def stable_day_statistics(
    item: params.StableDayStatisticsParams, result: data.Response = Depends(utils.response_obj)
):
    """
    获取失稳类型统计
    :param item: 参数
    :param result:
    :return:
    """
    # tvsi: 电压失稳， tsi: 功角失稳
    if not isinstance(common.tas_analyzer_mark_false, tsa_service.tsa_flow.Analyzer):
        result.code = 1002
        result.msg = "暂未查询"
        return result
    if item.mask_fault:
        tas_analyzer = common.tas_analyzer_mark_true
        if item.type == 1:
            # 人工智能
            df_stable = tas_analyzer.stability_table(common.ai_stability_mark_true)
        else:
            # 仿真
            df_stable = tas_analyzer.stability_table(common.bpa_stability_mark_true)
    else:
        tas_analyzer = common.tas_analyzer_mark_false
        if item.type == 1:
            # 人工智能
            df_stable = tas_analyzer.stability_table(common.ai_stability_mark_false)
        else:
            # 仿真
            df_stable = tas_analyzer.stability_table(common.bpa_stability_mark_false)

    tsi_list = []
    tvsi_list = []
    for name, group_df in df_stable[df_stable['电压失稳'] == True].groupby(["line", "type"]):
        obj_dict = {
            'value': name[0],
            'type': name[1],
            "count": len(group_df)
        }
        group_df['hour'] = pd.to_datetime(group_df['time']).dt.hour
        # 3. 按 'hour' 列进行分组，并进行聚合操作
        #    - 使用 agg() 函数可以同时执行多个聚合
        #    - 'count': 使用 'size' 统计每个小时分组的行数
        #    - 'times': 使用 'list' 将每个分组的 'time' 值聚合为一个列表
        hourly_stats = group_df.groupby('hour').agg(
            count=('time', 'size'), times=('time', lambda x: list(x))
        ).reset_index()
        # 4. 创建完整小时列表并合并
        all_hours = pd.DataFrame({'hour': range(24)})
        final_report = pd.merge(all_hours, hourly_stats, on='hour', how='left')

        # 5. 清理数据
        final_report['count'] = final_report['count'].fillna(0).astype(int)
        final_report['times'] = final_report['times'].apply(lambda x: x if isinstance(x, list) else [])

        # 6. 【关键步骤】将 'times' 列中的 Timestamp 对象列表转换为字符串列表
        #    我们对 'times' 列的每个列表进行操作，
        #    使用 strftime('%Y-%m-%d %H:%M:%S') 将每个 Timestamp 格式化为字符串。
        final_report['times'] = final_report['times'].apply(
            lambda timestamp_list: [ts.strftime('%Y-%m-%d %H:%M:%S') for ts in timestamp_list]
        )
        obj_dict['stats'] = final_report.to_dict("records")
        tvsi_list.append(obj_dict)
    for name, group_df in df_stable[df_stable['功角失稳'] == True].groupby(["line", "type"]):
        obj_dict = {
            'value': name[0],
            'type': name[1],
            "count": len(group_df)
        }
        group_df['hour'] = pd.to_datetime(group_df['time']).dt.hour
        # 3. 按 'hour' 列进行分组，并进行聚合操作
        #    - 使用 agg() 函数可以同时执行多个聚合
        #    - 'count': 使用 'size' 统计每个小时分组的行数
        #    - 'times': 使用 'list' 将每个分组的 'time' 值聚合为一个列表
        hourly_stats = group_df.groupby('hour').agg(
            count=('time', 'size'), times=('time', lambda x: list(x))
        ).reset_index()
        # 4. 创建完整小时列表并合并
        all_hours = pd.DataFrame({'hour': range(24)})
        final_report = pd.merge(all_hours, hourly_stats, on='hour', how='left')

        # 5. 清理数据
        final_report['count'] = final_report['count'].fillna(0).astype(int)
        final_report['times'] = final_report['times'].apply(lambda x: x if isinstance(x, list) else [])

        # 6. 【关键步骤】将 'times' 列中的 Timestamp 对象列表转换为字符串列表
        #    我们对 'times' 列的每个列表进行操作，
        #    使用 strftime('%Y-%m-%d %H:%M:%S') 将每个 Timestamp 格式化为字符串。
        final_report['times'] = final_report['times'].apply(
            lambda timestamp_list: [ts.strftime('%Y-%m-%d %H:%M:%S') for ts in timestamp_list]
        )
        obj_dict['stats'] = final_report.to_dict("records")
        tsi_list.append(obj_dict)
    data_dict = {"tsi": tsi_list, "tvsi": tvsi_list}
    result.data = data_dict
    return result


@router.post("/stable/day/analyze", tags=[common.JIANGSU_TAG], response_model=data.Response)
def stable_day_analyze(item: params.StableDayAnalyzeParams, result: data.Response = Depends(utils.response_obj)):
    """
    获取单日暂稳分析数据
    :param item: 参数
    :param result:
    :return:
    """
    data = tsa_service.get_stable_day_analyze(item.time_no)
    result.data = data
    return result


@router.post("/stable/newrate/capacity", tags=[common.JIANGSU_TAG], response_model=data.Response)
def stable_newrate_capacity(
    item: params.StableNewrateCapacityParams, result: data.Response = Depends(utils.response_obj)
):
    """
    获取新能源的发电同时率;常规机组的开机容量、以及负荷;
    :param item: 参数
    :param result:
    :return:
    """
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get("output", {})
    network = case.get("network")
    data = data_utils.get_zone_newrate_and_genoncapacity(
        net=network, result_dict=output, time_list=item.time_list
    )
    result.data = data
    return result


@router.get("/download/area/ele_inds", tags=[common.JIANGSU_TAG], response_model=data.Response)
def download_area_ele_inds(
    case_id: str, area: Optional[str] = None, result: data.Response = Depends(utils.response_obj)
):
    """
    下载分区裕度计算数据
    :param case_id: 算例id
    :param area: 分区名称
    :param result:
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    tp = int(time.time())
    area_dir = os.path.join(common.download_area_dirs, f"{area}_{tp}")
    if not os.path.isdir(area_dir):
        os.makedirs(area_dir, exist_ok=True)
    network = case.get("network")
    case_info = case.get('case_info', {})
    config = case_info.get("config", {})
    indicator = case.get("indicator", {})
    area_details_dict = case_info['zone']['device_relay']
    zip_path = f"{area}.zip"
    data_utils.get_zone_elements_and_inds(
        net=network,
        config=config,
        area_details_dict=area_details_dict,
        area_name=area,
        indicator_rlt_dict=indicator,
        save_dir=area_dir
    )
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(area_dir):
            for file in files:
                file_path = os.path.join(root, file)
                # 计算相对于指定目录的相对路径
                relative_path = os.path.relpath(file_path, area_dir)
                zipf.write(file_path, relative_path)
    return FileResponse(zip_path, filename=quote_plus(zip_path), media_type="application/json")


@router.get("/project/list", tags=[common.JIANGSU_TAG], response_model=data.Response)
def project_list(result: data.Response = Depends(utils.response_obj)):
    """
    获取工程列表
    :param item: 参数
    :param result:
    :return:
    """
    data_dict = {}
    for case_id, case in common.case_dict.items():
        comment = case.get('comment')
        if comment != "工程投产":
            continue
        year = case.get("year")
        if year not in data_dict:
            data_dict[year] = case
    data_list = []
    projects = project_service.get_projects()
    for _, obj in projects.items():
        if obj.get("year") not in data_dict:
            continue
        dispname = obj.get("dispname")
        if not dispname:
            continue
        case = data_dict[obj.get("year")]
        network = case.get('network', {})
        bus_df = network['bus']
        new_df = bus_df[bus_df['dispname'].str.contains(dispname, na=False)]
        if new_df.empty:
            continue
        lat = new_df['lat'].values[0]
        lon = new_df['lon'].values[0]
        obj['position'] = {"lat": lat, "lon": lon}
        data_list.append(obj)
    result.data = data_list
    return result


@router.get("/project/zone/all/psm", tags=[common.JIANGSU_TAG], response_model=data.Response)
def project_zone_all_psm(year: int, result: data.Response = Depends(utils.response_obj)):
    """
    获取工程投产-全省/所有分区的最小供电裕度/平均供电裕度, 有供电缺口时长/供电紧张时长
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    data_dict = {}
    for case_id, case in common.case_dict.items():
        comment = case.get('comment')
        if comment != "工程投产":
            continue
        case_year = case.get("year")
        if year != case_year:
            continue
        case_name = case.get('name')
        if "计划" in case_name:
            position = "left"
        else:
            position = "right"
        output = case.get('output', {})
        indicator = case.get("indicator", {})
        case_info = case.get("case_info", {})
        cal_result = case.get("cal_result", {})
        data = data_utils.get_allzone_psm_value(
            indicator_rlt_dict=indicator,
            result_dict=output,
            area_details=case_info['zone']['device_relay'],
            ana_result=cal_result['consump_rate']['zone']
        )
        data_dict[position] = data
    result.data = data_dict
    return result


@router.get("/project/zone/gap/code", tags=[common.JIANGSU_TAG], response_model=data.Response)
def project_zone_gap_code(year: int, area: str, result: data.Response = Depends(utils.response_obj)):
    """
    工程投产-全年供电缺口/调峰缺口信息的年-月-日,月-日-小时分布--单位:万千瓦;
    :params:
    case_id: case id
    :return: dict
    """
    data_dict = {}
    for case_id, case in common.case_dict.items():
        comment = case.get('comment')
        if comment != "工程投产":
            continue
        case_year = case.get("year")
        if year != case_year:
            continue
        case_name = case.get("name")
        if "计划" in case_name:
            postion = "left"
        else:
            postion = "right"
        case_info = case.get("case_info", {})
        config = case_info.get("config", {})
        indicator = case.get('indicator', {})
        data = balance_data_utils.get_zone_margin_qr_data(
            config=config, indicator_rlt_dict=indicator, area_name=area
        )
        time_range = []
        values = []
        for month, days in data[1].items():
            for day_index, day in enumerate(days):
                for hours_index, val_obj in enumerate(day):
                    flag = val_obj.get('dataFlag')
                    if flag == -1:
                        continue
                    new_val = val_obj.get("dataVal")
                    values.append(new_val)
                    t1 = f"{year}-{str(month).zfill(2)}-{str(day_index + 1).zfill(2)} {str(hours_index).zfill(2)}:00:00"
                    time_range.append(t1)
        data_dict[postion] = {"code": data, "time_range": time_range, "values": values}
    result.data = data_dict
    return result


@router.get("/project/zone/trafo/info", tags=[common.JIANGSU_TAG], response_model=data.Response)
def project_zone_trafo_info(year: int, area: str, result: data.Response = Depends(utils.response_obj)):
    """
    工程投产-潮流断面信息
    :params: year: 年份
    :params: area: 区域
    :return: result
    """
    data_dict = {}
    for case_id, case in common.case_dict.items():
        comment = case.get('comment')
        if comment != "工程投产":
            continue
        case_year = case.get("year")
        if year != case_year:
            continue
        case_name = case.get("name")
        if "计划" in case_name:
            postion = "left"
        else:
            postion = "right"
        network = case.get("network", {})
        case_info = case.get("case_info", {})
        area_details = case_info['zone']['device_relay']
        data = data_utils.get_zone_trafo_inf(net=network, area_details=area_details, area_name=area)
        data_dict[postion] = {"case_id": case_id, "data": data}
    result.data = data_dict
    return result


@router.get("/project/zone/data/diff", tags=[common.JIANGSU_TAG], response_model=data.Response)
def project_zone_data_diff(year: int, area: str, result: data.Response = Depends(utils.response_obj)):
    """
    工程投产-数据比对
    :param year: 年份
    :param area: 分区
    :param result: 返回值
    :return:
    """
    data_dict = {}
    for case_id, case in common.case_dict.items():
        comment = case.get('comment')
        if comment != "工程投产":
            continue
        case_year = case.get("year")
        if year != case_year:
            continue
        case_name = case.get("name")
        if "计划" in case_name:
            postion = "left"
        else:
            postion = "right"
        output = case.get('output', {})
        indicator = case.get("indicator", {})
        case_info = case.get("case_info", {})
        cal_result = case.get("cal_result", {})
        network = case.get("network", {})
        area_details = case_info['zone']['device_relay']
        data = data_utils.get_allzone_psm_value(
            indicator_rlt_dict=indicator,
            result_dict=output,
            area_details=area_details,
            ana_result=cal_result['consump_rate']['zone'],
            area_name=area
        )
        trafo_data = data_utils.get_zone_trafo_inf(net=network, area_details=area_details, area_name=area)
        index_list = []
        for _, v1 in trafo_data.items():
            for v2 in v1['trafo'].values():
                index_list.append(v2['trafo_interface'])
        p1 = output['interface_power'][index_list, :]
        maxp = network.interface.loc[index_list, 'max_p_mw'].reset_index(drop=True)
        max_cross_line_rate = (p1 / maxp[:, np.newaxis]).max()
        new_data = {
            "gap_power": data[area].get("min_psm"),
            "gap_electricity": data[area].get("load_cutial_electricity"),
            "gap_hours": data[area].get("load_cutial_hours"),
            "eq_limit_duration": data[area].get("elecSlack_equal_hours"),
            "economic": data[area].get("load_cutial_electricity") * 3,
            "max_cross_line_rate": max_cross_line_rate
        }
        data_dict[postion] = new_data
    result.data = data_dict
    return result


@router.get("/short/statistics", tags=[common.JIANGSU_TAG], response_model=data.Response)
def short_statistics(
    case_id: str, area: str = "全省", year: int = 2025, result: data.Response = Depends(utils.response_obj)
):
    """
    短路统计数据
    :param case_id: 算例id
    :param area: 分区
    :param result: 返回值
    :return:
    """
    if not isinstance(common.df_short_data, pd.DataFrame):
        result.code = 1002
        result.msg = "暂无短路数据"
        return result
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get("case_info", {})
    network = case.get("network", {})
    area_details = case_info['zone']['device_relay']
    df = common.df_short_data
    if area != "全省":
        bus_index = area_details[area]['bus']
        names = network.bus.iloc[bus_index]['name'].tolist()
        df = df[df['area'].isin(set(names))]
        # 电压过滤
        df = df[df['base_voltage'].eq(230)]
        df1 = df[(df['ik'].ge(48.5)) & (df['ik'].lt(50))]
        df2 = df[df['ik'].ge(50)]
        df_other = df[df['ik'].lt(48.5)]
    else:
        df_bus = network.bus[['name', 'lat', 'lon', 'dispname', 'vn_kv']]
        names = set(df_bus['name'].tolist())
        df = df[df['area'].isin(names)]
        df = df[df['base_voltage'].eq(525)]
        df1 = df[(df['ik'].ge(61.5)) & (df['ik'].lt(63))]
        df2 = df[df['ik'].ge(63)]
        df_other = df[df['ik'].lt(61.5)]


    one_number = len(df2)
    two_number = len(df1)

    # 提取月份和天
    df1['month'] = df1['date'].dt.month
    df1['day'] = df1['date'].dt.day
    df2['month'] = df2['date'].dt.month
    df2['day'] = df2['date'].dt.day
    df_other['month'] = df_other['date'].dt.month
    df_other['day'] = df_other['date'].dt.day

    df_code = {}
    date_range = pd.date_range(start=f'{year}-01-01', end=f'{year}-12-31', freq='D').astype(str).tolist()
    # 按月份和天分组，计算每天符合条件的数量
    for month in range(1, 13):
        month_list = []
        for day in range(1, 32):
            date_value = f"{year}-{month:02}-{day:02}"
            # 过滤当前月和天的数据
            daily_data1 = df1[(df1['month'] == month) & (df1['day'] == day)]
            daily_data2 = df2[(df2['month'] == month) & (df2['day'] == day)]
            if date_value not in date_range:
                flag = -1
                value = -1
            elif not daily_data1.empty:
                flag = 1
                value= daily_data1['ik'].max()
            elif not daily_data2.empty:
                flag = 2
                value = daily_data2['ik'].max()
            else:
                flag = 0
                value = df_other[(df_other['month'] == month) & (df_other['day'] == day)]['ik'].max()
            month_list.append({"flag": flag, "value": value})
        df_code[month] = month_list
    result.data = {"ome_number": one_number, "two_number": two_number, "code": df_code}
    return result


@router.post("/short/station/list", tags=[common.JIANGSU_TAG], response_model=data.Response)
def short_station_list(item: params.ShortStationListParams, result: data.Response = Depends(utils.response_obj)):
    """
    短路电流-厂站列表
    :param item: 参数
    :param result: 返回值
    :return:
    """
    if not isinstance(common.df_short_data, pd.DataFrame):
        result.code = 1002
        result.msg = "暂无短路数据"
        return result
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get("case_info", {})
    network = case.get("network", {})
    area_details = case_info['zone']['device_relay']
    df = common.df_short_data
    # 日期过滤
    if item.time_idx:
        df = df[df['time_no'].isin(item.time_idx)]

    start_idx = (item.page - 1) * item.size
    end_idx = start_idx + item.size
    # 分区过滤
    if item.area != "全省":
        df = df[df['base_voltage'].eq(230)]
        df = df[df['ik'].ge(48.5)]
        bus_index = area_details[item.area]['bus']
        df_bus = network.bus.iloc[bus_index][['name', 'vn_kv', 'lat', 'lon', 'dispname']]
        names = set(df_bus['name'].tolist())
        df = df[df['area'].isin(names)]
        total = len(df)
    else:
        df = df[df['base_voltage'].eq(525)]
        df = df[df['ik'].ge(61.5)]
        df_bus = network.bus[['name', 'lat', 'lon', 'dispname', 'vn_kv']]
        names = set(df_bus['name'].tolist())
        df = df[df['area'].isin(names)]
        total = len(df)
        df = df.iloc[start_idx:end_idx]

    df = pd.merge(df, df_bus, left_on="area", right_on="name", how='left')
    if item.area != "全省":
        df = df.iloc[start_idx:end_idx]
    df = df[['date', 'dispname', 'bus_name', 'ik', 'vn_kv']]
    df['date'] = df['date'].dt.strftime('%Y-%m-%d %H:%M:%S').astype(str)
    result.data = {"total": total, "records": df.to_dict(orient='records')}
    return result


@router.post("/short/map", tags=[common.JIANGSU_TAG], response_model=data.Response)
def short_map(item: params.ShortMapValueParams, result: data.Response = Depends(utils.response_obj)):
    """
    短路电流-地图中的数值
    :param item: 参数
    :param result: 返回值
    :return:
    """
    if not isinstance(common.df_short_data, pd.DataFrame):
        result.code = 1002
        result.msg = "暂无短路数据"
        return result
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get("case_info", {})
    network = case.get("network", {})
    output = case.get("output")
    area_details = case_info['zone']['device_relay']
    df = common.df_short_data
    # 分区过滤
    if item.area != "全省":
        bus_index = area_details[item.area]['bus']
        df_bus = network.bus.iloc[bus_index][['name', 'vn_kv', 'lat', 'lon', 'dispname']]
        df_bus = df_bus[df_bus['vn_kv'].eq(230)]
        names = set(df_bus['name'].tolist())
        df = df[df['area'].isin(names)]
    else:
        df_bus = network.bus[['name', 'vn_kv', 'lat', 'lon', 'dispname']]
        df_bus = df_bus[df_bus['vn_kv'].eq(525)]
        names = set(df_bus['name'].tolist())
        df = df[df['area'].isin(names)]
    max_indexs = df.groupby("area")['ik'].idxmax()
    df = df.loc[max_indexs]
    df = pd.merge(df, df_bus, left_on="area", right_on="name", how='left')
    df = df.replace('', np.nan)
    df = df.dropna(subset=['dispname'])
    map_data = df.to_dict("records")
    if item.area != "全省":
        disp_dict = defaultdict(list)
        for obj in map_data:
            obj_dict = {
                "name": obj['bus_name'],
                "value": obj['ik'],
                "vn_kv": obj['vn_kv'],
                "position": [obj['lon'], obj['lat'], 0]
            }
            disp_dict[obj['dispname']].append(obj_dict)

        # 获取网架信息
        network_map = data_utils.get_network_topo(
            case_info_dict=case_info, result_dict=output, area_name=item.area
        )
        for point in network_map['point_data']:
            disp_value_list = disp_dict.pop(point['name'], [])
            max_value_list = []
            max_value = 0
            for v1 in disp_value_list:
                max_value_list.append(v1['value'])
            if max_value_list:
                max_value = max(max_value_list)
            point['ik_list'] = disp_value_list
            point['max_value'] = max_value
        for disp_key, disp_value in disp_dict.items():
            max_value = max([obj['value'] for obj in disp_value])
            new_point = {
                "name": disp_key,
                "vn_kv": disp_value[0]['vn_kv'],
                "value": disp_value[0]['position'],
                "ik_list": disp_value,
                "max_value": max_value
            }
            network_map['point_data'].append(new_point)
        result.data = network_map
        return result
    result.data = map_data
    return result


@router.post("/short/gen/capacity", tags=[common.JIANGSU_TAG], response_model=data.Response)
def short_gen_capacity(item: params.ShortGenCapacityParams, result: data.Response = Depends(utils.response_obj)):
    """
    短路电流-开机容量曲线
    :param item: 参数
    :param result: 返回值
    :return:
    """
    if not isinstance(common.df_short_data, pd.DataFrame):
        result.code = 1002
        result.msg = "暂无短路数据"
        return result
    if item.case_id in common.case_dict:
        case = common.case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get("output")
    case_info = case.get("case_info", {})
    network = case.get("network", {})
    area_details = case_info['zone']['device_relay']
    # 计算开机容量
    gen_capacity = output.get("gen_state") * network.gen['max_p_mw'].values.reshape(-1, 1)
    gen_df = pd.DataFrame(gen_capacity)
    df = common.df_short_data
    if item.area != "全省":
        bus_index = area_details[item.area]['bus']
        df_bus = network.bus.iloc[bus_index]
        names = set(df_bus['name'].tolist())
        df = df[df['area'].isin(names)]
    if item.time_idx:
        # 获取某一天的24个时刻的数据
        daily_data = gen_df.iloc[:, item.time_idx]
    else:
        # 计算短路电流最大的那一天的24个时刻的数据
        max_row = df.loc[df['ik'].idxmax()]
        day_number = max_row['time_no'] // 24 + 1
        # 计算该天的起始小时和结束小时
        start_hour = (day_number - 1) * 24 + 1
        end_hour = day_number * 24
        # 提取该天的所有小时数据
        daily_data = gen_df.iloc[:, start_hour:end_hour + 1]
    if item.area != "全省":
        gen_indexs = area_details[item.area]['gen']
        daily_data = daily_data.iloc[gen_indexs]
    gen_series = daily_data.sum(axis=0)
    result.data = gen_series.tolist()
    return result


@router.post("/short/area/trafo", tags=[common.JIANGSU_TAG], response_model=data.Response)
def short_area_trafo(item: params.ShortAreaTrafoParams, result: data.Response = Depends(utils.response_obj)):
    """
    短路电流-分区主变容量
    :param item: 参数
    :param result: 返回值
    :return:
    """
    return result
